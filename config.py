import os
from dotenv import load_dotenv

load_dotenv()

# LLM Configuration
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************.ED2fuIwehJ6fbi9PWGY7jgGCOKDGIJ19BbN4YEZHCjE")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://muses.weizhipin.com/muses-open/openai/v1")
MODEL_NAME = os.getenv("OPENAI_MODEL", "deepseek-v3-0324")

# Playwright Configuration
PLAYWRIGHT_HEADLESS = os.getenv("PLAYWRIGHT_HEADLESS", "false").lower() == "true"
PLAYWRIGHT_TIMEOUT = int(os.getenv("PLAYWRIGHT_TIMEOUT", "30000"))
