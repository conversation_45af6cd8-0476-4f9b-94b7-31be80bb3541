from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class TestScenario(str, Enum):
    """测试场景类型"""
    NEW_TEST = "new_test"
    MODIFY_SCRIPT = "modify_script"
    DEBUG_ERROR = "debug_error"
    OPTIMIZE_PERFORMANCE = "optimize_performance"


class ErrorType(str, Enum):
    """错误类型"""
    SYNTAX_ERROR = "syntax_error"
    RUNTIME_ERROR = "runtime_error"
    LOGIC_ERROR = "logic_error"
    ELEMENT_NOT_FOUND = "element_not_found"
    TIMEOUT_ERROR = "timeout_error"


class TestRequest(BaseModel):
    """测试请求数据结构"""
    dom_info: str = Field(description="页面DOM结构信息")
    user_requirements: str = Field(description="用户测试要求描述")
    existing_script: Optional[str] = Field(default=None, description="现有的测试脚本")
    error_info: Optional[str] = Field(default=None, description="异常错误信息")


class ElementInfo(BaseModel):
    """元素信息"""
    selector: str = Field(description="元素选择器")
    selector_type: str = Field(description="选择器类型：css, xpath, text等")
    element_type: str = Field(description="元素类型：button, input, link等")
    action: str = Field(description="要执行的操作：click, fill, select等")
    value: Optional[str] = Field(default=None, description="操作值（如输入文本）")


class TestStep(BaseModel):
    """测试步骤"""
    step_number: int = Field(description="步骤序号")
    description: str = Field(description="步骤描述")
    element: Optional[ElementInfo] = Field(default=None, description="目标元素")
    action: str = Field(description="操作类型")
    expected_result: Optional[str] = Field(default=None, description="预期结果")
    wait_condition: Optional[str] = Field(default=None, description="等待条件")


class TestResult(BaseModel):
    """测试执行结果"""
    success: bool = Field(description="是否成功")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    execution_time: float = Field(description="执行时间（秒）")
    screenshot_path: Optional[str] = Field(default=None, description="截图路径")
    logs: List[str] = Field(default_factory=list, description="执行日志")


class AgentState(BaseModel):
    """智能体状态"""
    # 输入数据
    request: TestRequest = Field(description="原始请求")
    
    # 分析结果
    scenario: Optional[TestScenario] = Field(default=None, description="识别的测试场景")
    test_steps: List[TestStep] = Field(default_factory=list, description="测试步骤")
    target_elements: List[ElementInfo] = Field(default_factory=list, description="目标元素")
    
    # 代码生成
    generated_code: Optional[str] = Field(default=None, description="生成的测试代码")
    code_modifications: List[str] = Field(default_factory=list, description="代码修改记录")
    
    # 执行结果
    test_result: Optional[TestResult] = Field(default=None, description="测试执行结果")
    
    # 状态标志
    has_errors: bool = Field(default=False, description="是否有错误")
    needs_optimization: bool = Field(default=False, description="是否需要优化")
    test_passed: bool = Field(default=False, description="测试是否通过")
    retry_count: int = Field(default=0, description="重试次数")
    max_retries: int = Field(default=3, description="最大重试次数")
    
    # 其他信息
    analysis_result: Optional[Dict[str, Any]] = Field(default=None, description="分析结果")
    suggestions: List[str] = Field(default_factory=list, description="改进建议")
    error_diagnosis: Optional[Dict[str, Any]] = Field(default=None, description="错误诊断")


class CodeTemplate(BaseModel):
    """代码模板"""
    name: str = Field(description="模板名称")
    template: str = Field(description="模板内容")
    variables: Dict[str, str] = Field(description="模板变量")
    description: str = Field(description="模板描述")


class PlaywrightConfig(BaseModel):
    """Playwright配置"""
    headless: bool = Field(default=False, description="是否无头模式")
    timeout: int = Field(default=30000, description="超时时间（毫秒）")
    viewport_width: int = Field(default=1280, description="视窗宽度")
    viewport_height: int = Field(default=720, description="视窗高度")
    browser_type: str = Field(default="chromium", description="浏览器类型")
