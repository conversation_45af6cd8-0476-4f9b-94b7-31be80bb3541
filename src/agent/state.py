from typing import TypedDict, List, Optional, Dict, Any
from src.models.schemas import (
    TestRequest, TestScenario, TestStep, ElementInfo, 
    TestResult, CodeTemplate, PlaywrightConfig
)


class PlaywrightAgentState(TypedDict):
    """Playwright智能体的状态类型定义"""
    
    # 输入数据
    request: TestRequest
    
    # 分析结果
    scenario: Optional[TestScenario]
    test_steps: List[TestStep]
    target_elements: List[ElementInfo]
    
    # 代码生成
    generated_code: Optional[str]
    code_modifications: List[str]
    
    # 执行结果
    test_result: Optional[TestResult]
    
    # 状态标志
    has_errors: bool
    needs_optimization: bool
    test_passed: bool
    retry_count: int
    max_retries: int
    
    # 其他信息
    analysis_result: Optional[Dict[str, Any]]
    suggestions: List[str]
    error_diagnosis: Optional[Dict[str, Any]]
    
    # 配置
    config: PlaywrightConfig


def create_initial_state(request: TestRequest) -> PlaywrightAgentState:
    """创建初始状态"""
    return PlaywrightAgentState(
        request=request,
        scenario=None,
        test_steps=[],
        target_elements=[],
        generated_code=None,
        code_modifications=[],
        test_result=None,
        has_errors=False,
        needs_optimization=False,
        test_passed=False,
        retry_count=0,
        max_retries=3,
        analysis_result=None,
        suggestions=[],
        error_diagnosis=None,
        config=PlaywrightConfig()
    )
