"""LangGraph工作流图构建"""

from typing import Dict, Any, List
from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from src.agent.state import PlaywrightAgentState, create_initial_state
from src.agent.nodes import PlaywrightAgentNodes
from src.models.schemas import TestRequest


class PlaywrightAgent:
    """Playwright自动化测试智能体"""
    
    def __init__(self):
        self.nodes = PlaywrightAgentNodes()
        self.graph = None
        self.app = None
        self._build_graph()
    
    def _build_graph(self):
        """构建LangGraph工作流图"""
        # 创建状态图
        workflow = StateGraph(PlaywrightAgentState)
        
        # 添加节点
        workflow.add_node("intelligent_analyze", self.nodes.intelligent_analyze)
        workflow.add_node("code_generation", self.nodes.code_generation)
        workflow.add_node("execute_test", self.nodes.execute_test)
        workflow.add_node("analyze_results", self.nodes.analyze_results)
        workflow.add_node("handle_errors", self.nodes.handle_errors)
        
        # 设置入口点
        workflow.set_entry_point("intelligent_analyze")
        
        # 添加边 - 智能分析 -> 代码生成
        workflow.add_edge("intelligent_analyze", "code_generation")
        
        # 添加条件边 - 代码生成后的路由
        workflow.add_conditional_edges(
            "code_generation",
            self._route_after_generation,
            {
                "execute_test": "execute_test",
                "END": END
            }
        )
        
        # 执行测试 -> 结果分析
        workflow.add_edge("execute_test", "analyze_results")
        
        # 添加条件边 - 结果分析后的路由
        workflow.add_conditional_edges(
            "analyze_results",
            self.nodes.decide_next_action,
            {
                "handle_errors": "handle_errors",
                "execute_test": "execute_test",
                "optimize_code": "code_generation",
                "END": END
            }
        )
        
        # 异常处理后的路由
        workflow.add_conditional_edges(
            "handle_errors",
            self._route_after_error_handling,
            {
                "execute_test": "execute_test",
                "code_generation": "code_generation",
                "END": END
            }
        )
        
        # 编译图
        memory = MemorySaver()
        self.app = workflow.compile(checkpointer=memory)
        self.graph = workflow
    
    def _route_after_generation(self, state: PlaywrightAgentState) -> str:
        """代码生成后的路由决策"""
        if not state["generated_code"]:
            print("❌ 代码生成失败，结束流程")
            return "END"
        
        if state["has_errors"]:
            print("⚠️ 代码生成时发现错误")
            return "END"
        
        print("✅ 代码生成成功，准备执行测试")
        return "execute_test"
    
    def _route_after_error_handling(self, state: PlaywrightAgentState) -> str:
        """异常处理后的路由决策"""
        if state["retry_count"] >= state["max_retries"]:
            print("🛑 达到最大重试次数，结束流程")
            return "END"
        
        if not state["has_errors"] and state["generated_code"]:
            print("🔧 错误已修复，重新执行测试")
            return "execute_test"
        
        if state["has_errors"]:
            print("⚠️ 仍有错误，尝试重新生成代码")
            return "code_generation"
        
        return "END"
    
    async def run(self, request: TestRequest, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """运行智能体"""
        print("🚀 Playwright自动化测试智能体开始运行...")
        
        # 创建初始状态
        initial_state = create_initial_state(request)
        
        # 应用配置
        if config:
            # 创建新的配置对象，合并原配置和新配置
            current_config = initial_state["config"]
            updated_config = current_config.model_copy(update=config)
            initial_state["config"] = updated_config
        
        # 运行工作流
        thread_config = {"configurable": {"thread_id": "playwright_agent_session"}}
        
        try:
            final_state = None
            step_count = 0
            max_steps = 20  # 防止无限循环
            
            async for step in self.app.astream(initial_state, thread_config):
                step_count += 1
                if step_count > max_steps:
                    print("⚠️ 达到最大步骤数，强制结束")
                    break
                
                node_name = list(step.keys())[0]
                node_state = step[node_name]
                
                print(f"📍 步骤 {step_count}: {node_name}")
                
                # 记录最终状态

                final_state = node_state
                
                # 如果流程结束，跳出循环
                if node_name == END:
                    break
            
            if final_state is None:
                final_state = initial_state
            
            # 生成结果报告
            result_report = self._generate_result_report(final_state)
            
            print("🎯 智能体运行完成")
            return result_report
            
        except Exception as e:
            print(f"❌ 智能体运行失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "generated_code": None,
                "test_result": None,
                "suggestions": [f"智能体运行失败: {str(e)}"]
            }
    
    def _generate_result_report(self, state: PlaywrightAgentState) -> Dict[str, Any]:
        """生成结果报告"""
        test_result = state.get("test_result")
        
        report = {
            "success": state.get("test_passed", False),
            "scenario": state.get("scenario"),
            "generated_code": state.get("generated_code"),
            "test_result": test_result.dict() if test_result else None,
            "execution_time": test_result.execution_time if test_result else 0,
            "retry_count": state.get("retry_count", 0),
            "suggestions": state.get("suggestions", []),
            "error_diagnosis": state.get("error_diagnosis"),
            "target_elements": [elem.dict() for elem in state.get("target_elements", [])],
            "test_steps": [step.dict() for step in state.get("test_steps", [])],
            "code_modifications": state.get("code_modifications", []),
            "analysis_result": state.get("analysis_result")
        }
        
        # 添加总结信息
        if report["success"]:
            report["summary"] = f"✅ 测试成功完成！执行时间: {report['execution_time']:.2f}秒"
        else:
            error_msg = state.get("error_diagnosis", {}).get("message", "未知错误")
            report["summary"] = f"❌ 测试失败: {error_msg}"
        
        return report
    
    def get_graph_visualization(self) -> str:
        """获取工作流图的可视化表示"""
        return """
        Playwright自动化测试智能体工作流：
        
        START
          ↓
        [智能分析] - 解析需求和DOM信息
          ↓
        [代码生成] - 生成或修改测试代码
          ↓
        [执行测试] - 运行Playwright测试
          ↓
        [结果分析] - 分析测试结果
          ↓
        [异常处理] ←→ (如果有错误且未超过重试次数)
          ↓
        END
        
        节点说明：
        - 智能分析：识别测试场景，提取目标元素和测试步骤
        - 代码生成：基于分析结果生成高质量的Playwright测试代码
        - 执行测试：运行测试代码并捕获结果
        - 结果分析：分析成功/失败原因，判断是否需要重试
        - 异常处理：智能修复错误，提供改进建议
        """
    
    def get_capabilities(self) -> Dict[str, List[str]]:
        """获取智能体能力说明"""
        return {
            "支持的测试场景": [
                "新建测试用例",
                "修改现有脚本", 
                "调试异常问题",
                "优化性能问题"
            ],
            "DOM分析能力": [
                "自动识别表单元素",
                "提取交互元素",
                "分析页面结构",
                "生成最佳选择器"
            ],
            "代码生成特性": [
                "符合Playwright最佳实践",
                "智能元素定位",
                "异常处理机制",
                "性能优化建议"
            ],
            "错误处理能力": [
                "智能错误诊断",
                "自动修复建议",
                "重试机制",
                "降级策略"
            ],
            "质量保证": [
                "代码语法验证",
                "逻辑合理性检查",
                "性能分析",
                "安全性检查"
            ]
        }


# 创建全局智能体实例
playwright_agent = PlaywrightAgent()


async def run_playwright_agent(request: TestRequest, config: Dict[str, Any] = None) -> Dict[str, Any]:
    """运行Playwright智能体的便捷函数"""
    return await playwright_agent.run(request, config)


def get_agent_info() -> Dict[str, Any]:
    """获取智能体信息"""
    return {
        "name": "Playwright自动化测试智能体",
        "version": "1.0.0",
        "description": "基于LangGraph的通用Playwright自动化测试智能体",
        "capabilities": playwright_agent.get_capabilities(),
        "workflow": playwright_agent.get_graph_visualization()
    }
