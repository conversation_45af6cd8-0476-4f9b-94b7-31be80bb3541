"""LangGraph工作流节点实现"""

import json
from typing import Dict, Any, List
from langchain_openai import Chat<PERSON>penAI
from langchain.schema import HumanMessage, SystemMessage

from config import OPENAI_API_KEY, OPENAI_BASE_URL, MODEL_NAME
from src.agent.state import PlaywrightAgentState
from src.models.schemas import TestScenario, TestStep, ElementInfo, TestResult, ErrorType
from src.tools.playwright_runner import PlaywrightRunner
from src.tools.dom_analyzer import DOMAnalyzer
from src.tools.code_validator import CodeValidator
from src.prompts.analyze import ANALYZE_REQUIREMENTS_PROMPT, ANALYZE_ERRORS_PROMPT, OPTIMIZE_CODE_PROMPT
from src.prompts.generate import GENERATE_CODE_PROMPT, MODIFY_CODE_PROMPT
from src.prompts.debug import DEBUG_ERROR_PROMPT


class PlaywrightAgentNodes:
    """Playwright智能体节点实现"""
    
    def __init__(self):
        self.llm = ChatOpenAI(
            model=MODEL_NAME,
            api_key=OPENAI_API_KEY,
            base_url=OPENAI_BASE_URL,
            temperature=0.1
        )
        self.dom_analyzer = DOMAnalyzer()
        self.code_validator = CodeValidator()
        self.playwright_runner = None
    
    async def intelligent_analyze(self, state: PlaywrightAgentState) -> PlaywrightAgentState:
        """智能分析节点 - 分析用户需求和DOM信息"""
        print("🔍 开始智能分析...")
        
        try:
            # 分析DOM结构
            dom_analysis = {}
            if state["request"].dom_info:
                dom_analysis = self.dom_analyzer.parse_dom(state["request"].dom_info)
            
            # 构建分析提示词
            prompt = ANALYZE_REQUIREMENTS_PROMPT.format(
                dom_info=state["request"].dom_info or "未提供",
                user_requirements=state["request"].user_requirements,
                existing_script=state["request"].existing_script or "无",
                error_info=state["request"].error_info or "无"
            )
            
            # 调用LLM进行分析
            messages = [
                SystemMessage(content="你是一个专业的Playwright自动化测试专家，负责分析测试需求。"),
                HumanMessage(content=prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            analysis_text = response.content
            
            # 解析分析结果
            analysis_result = self._parse_analysis_result(analysis_text)
            
            # 更新状态
            state["scenario"] = TestScenario(analysis_result.get("scenario", "new_test"))
            state["target_elements"] = [
                ElementInfo(**elem) for elem in analysis_result.get("target_elements", [])
            ]
            state["test_steps"] = [
                TestStep(**step) for step in analysis_result.get("test_steps", [])
            ]
            state["analysis_result"] = {
                "dom_analysis": dom_analysis,
                "llm_analysis": analysis_result
            }
            state["suggestions"] = analysis_result.get("suggestions", [])
            
            print(f"✅ 分析完成 - 场景: {state['scenario']}, 步骤数: {len(state['test_steps'])}")
            
        except Exception as e:
            print(f"❌ 分析失败: {str(e)}")
            state["has_errors"] = True
            state["error_diagnosis"] = {
                "error_type": "analysis_error",
                "message": str(e)
            }
        
        return state
    
    async def code_generation(self, state: PlaywrightAgentState) -> PlaywrightAgentState:
        """代码生成节点 - 生成或修改测试代码"""
        print("🔧 开始代码生成...")
        
        try:
            scenario = state["scenario"]
            
            if scenario == TestScenario.MODIFY_SCRIPT:
                # 修改现有脚本
                generated_code = await self._modify_existing_code(state)
            else:
                # 生成新代码
                generated_code = await self._generate_new_code(state)
            
            # 验证生成的代码
            validation_result = self.code_validator.validate_playwright_code(generated_code)
            
            # 更新状态
            state["generated_code"] = generated_code
            state["code_modifications"].append(f"生成代码 - 场景: {scenario}")
            
            # 如果验证失败，添加建议
            if not validation_result["is_valid"]:
                state["suggestions"].extend(validation_result["suggestions"])
                print(f"⚠️ 代码验证发现问题，分数: {validation_result['score']}")
            else:
                print(f"✅ 代码生成完成，验证分数: {validation_result['score']}")
            
        except Exception as e:
            print(f"❌ 代码生成失败: {str(e)}")
            state["has_errors"] = True
            state["error_diagnosis"] = {
                "error_type": "generation_error",
                "message": str(e)
            }
        
        return state
    
    async def execute_test(self, state: PlaywrightAgentState) -> PlaywrightAgentState:
        """执行测试节点 - 运行Playwright测试"""
        print("🚀 开始执行测试...")
        
        try:
            if not state["generated_code"]:
                raise Exception("没有可执行的测试代码")
            
            # 初始化Playwright执行器
            if not self.playwright_runner:
                self.playwright_runner = PlaywrightRunner(state["config"])
            
            # 执行测试
            test_result = await self.playwright_runner.execute_test(
                state["generated_code"],
                f"test_{state['scenario']}_attempt_{state['retry_count']}"
            )
            
            # 更新状态
            state["test_result"] = test_result
            state["test_passed"] = test_result.success
            state["has_errors"] = not test_result.success
            
            if test_result.success:
                print(f"✅ 测试执行成功，耗时: {test_result.execution_time:.2f}秒")
            else:
                print(f"❌ 测试执行失败: {test_result.error_message}")
                
        except Exception as e:
            print(f"❌ 测试执行异常: {str(e)}")
            state["has_errors"] = True
            state["test_result"] = TestResult(
                success=False,
                error_message=str(e),
                execution_time=0.0,
                logs=[f"执行异常: {str(e)}"]
            )
        
        return state
    
    async def analyze_results(self, state: PlaywrightAgentState) -> PlaywrightAgentState:
        """结果分析节点 - 分析测试结果"""
        print("📊 开始结果分析...")
        
        try:
            test_result = state["test_result"]
            
            if test_result.success:
                # 成功时的分析
                state["suggestions"].append("测试执行成功！")
                
                # 检查是否需要优化
                if test_result.execution_time > 10.0:
                    state["needs_optimization"] = True
                    state["suggestions"].append("测试执行时间较长，建议优化性能")
                
            else:
                # 失败时的详细分析
                error_analysis = await self._analyze_test_failure(state)
                state["error_diagnosis"] = error_analysis
                
                # 判断是否可以重试
                if (state["retry_count"] < state["max_retries"] and 
                    error_analysis.get("retry_recommended", False)):
                    print(f"📝 准备重试 ({state['retry_count'] + 1}/{state['max_retries']})")
                else:
                    print("🛑 达到最大重试次数或不建议重试")
            
        except Exception as e:
            print(f"❌ 结果分析失败: {str(e)}")
            state["error_diagnosis"] = {
                "error_type": "analysis_error",
                "message": str(e)
            }
        
        return state
    
    async def handle_errors(self, state: PlaywrightAgentState) -> PlaywrightAgentState:
        """异常处理节点 - 处理测试失败和错误"""
        print("🔧 开始异常处理...")
        
        try:
            # 增加重试次数
            state["retry_count"] += 1
            
            test_result = state["test_result"]
            error_diagnosis = state["error_diagnosis"]
            
            if test_result and not test_result.success:
                # 基于错误类型进行修复
                fix_suggestions = await self._get_fix_suggestions(state)
                
                if fix_suggestions and state["retry_count"] <= state["max_retries"]:
                    # 应用修复建议
                    fixed_code = await self._apply_fixes(state, fix_suggestions)
                    state["generated_code"] = fixed_code
                    state["code_modifications"].append(f"错误修复 - 重试 {state['retry_count']}")
                    
                    # 重置错误状态，准备重试
                    state["has_errors"] = False
                    state["test_passed"] = False
                    
                    print(f"🔧 应用修复建议，准备重试")
                else:
                    print("🛑 无法修复或达到最大重试次数")
            
        except Exception as e:
            print(f"❌ 异常处理失败: {str(e)}")
            state["error_diagnosis"] = {
                "error_type": "handle_error_failed",
                "message": str(e)
            }
        
        return state
    
    def decide_next_action(self, state: PlaywrightAgentState) -> str:
        """决策下一步行动"""
        if state["test_passed"]:
            print("🎉 测试通过，流程结束")
            return "END"
        
        if state["has_errors"]:
            if state["retry_count"] < state["max_retries"]:
                print(f"🔄 检测到错误，进入异常处理 (重试 {state['retry_count'] + 1}/{state['max_retries']})")
                return "handle_errors"
            else:
                print("🛑 达到最大重试次数，流程结束")
                return "END"
        
        if state["needs_optimization"]:
            print("⚡ 需要性能优化")
            return "optimize_code"
        
        if not state["generated_code"]:
            print("📝 需要生成代码")
            return "code_generation"
        
        print("🚀 执行测试")
        return "execute_test"
    
    # 辅助方法
    
    def _parse_analysis_result(self, analysis_text: str) -> Dict[str, Any]:
        """解析LLM分析结果"""
        try:
            # 尝试解析JSON格式的结果
            if "```json" in analysis_text:
                json_start = analysis_text.find("```json") + 7
                json_end = analysis_text.find("```", json_start)
                json_text = analysis_text[json_start:json_end].strip()
                return json.loads(json_text)
            
            # 如果不是JSON格式，尝试从文本中提取信息
            return self._extract_analysis_from_text(analysis_text)
            
        except Exception as e:
            print(f"⚠️ 解析分析结果失败: {str(e)}")
            return {
                "scenario": "new_test",
                "target_elements": [],
                "test_steps": [],
                "suggestions": ["解析分析结果时出现问题，使用默认配置"]
            }
    
    def _extract_analysis_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中提取分析信息"""
        # 简化的文本解析逻辑
        result = {
            "scenario": "new_test",
            "target_elements": [],
            "test_steps": [],
            "suggestions": []
        }
        
        # 根据关键词判断场景
        if "修改" in text or "modify" in text.lower():
            result["scenario"] = "modify_script"
        elif "调试" in text or "debug" in text.lower():
            result["scenario"] = "debug_error"
        elif "优化" in text or "optimize" in text.lower():
            result["scenario"] = "optimize_performance"
        
        return result
    
    async def _generate_new_code(self, state: PlaywrightAgentState) -> str:
        """生成新的测试代码"""
        # 生成测试名称
        test_name = f"测试_{state['scenario'].value}"
        
        # 获取配置信息
        config = state["config"]
        
        # 生成步骤代码和断言代码的示例
        test_steps_code = "// 基于分析结果生成的测试步骤"
        assertions_code = "// 基于测试步骤生成的断言验证"
        
        try:
            prompt = GENERATE_CODE_PROMPT.format(
                scenario=state["scenario"],
                test_steps=json.dumps([step.dict() for step in state["test_steps"]], ensure_ascii=False),
                target_elements=json.dumps([elem.dict() for elem in state["target_elements"]], ensure_ascii=False),
                existing_code=state["request"].existing_script or "无",
                config=config.dict()
            )
        except KeyError as e:
            print(f"❌ 提示词格式化失败: {str(e)}")
            raise e
        
        messages = [
            SystemMessage(content="你是一个Playwright测试代码生成专家。"),
            HumanMessage(content=prompt)
        ]
        
        response = await self.llm.ainvoke(messages)
        print(f"🔍 LLM 响应内容（前500字符）: {response.content[:500]}")
        extracted_code = self._extract_code_from_response(response.content)
        print(f"🔍 提取的代码（前200字符）: {extracted_code[:200]}")
        return extracted_code
    
    async def _modify_existing_code(self, state: PlaywrightAgentState) -> str:
        """修改现有代码"""
        prompt = MODIFY_CODE_PROMPT.format(
            original_code=state["request"].existing_script,
            modification_requirements=state["request"].user_requirements,
            error_info=state["request"].error_info or "无",
            dom_changes="DOM信息已更新" if state["request"].dom_info else "无DOM变化"
        )
        
        messages = [
            SystemMessage(content="你是一个Playwright测试代码修改专家。"),
            HumanMessage(content=prompt)
        ]
        
        response = await self.llm.ainvoke(messages)
        
        # 尝试从JSON响应中提取代码
        try:
            if "```json" in response.content:
                json_start = response.content.find("```json") + 7
                json_end = response.content.find("```", json_start)
                json_text = response.content[json_start:json_end].strip()
                result = json.loads(json_text)
                return result.get("modified_code", response.content)
        except:
            pass
        
        return self._extract_code_from_response(response.content)
    
    def _extract_code_from_response(self, response_text: str) -> str:
        """从LLM响应中提取代码"""
        # 查找JavaScript代码块
        if "```javascript" in response_text:
            start = response_text.find("```javascript") + 13
            end = response_text.find("```", start)
            return response_text[start:end].strip()
        elif "```js" in response_text:
            start = response_text.find("```js") + 5
            end = response_text.find("```", start)
            return response_text[start:end].strip()
        elif "```" in response_text:
            start = response_text.find("```") + 3
            end = response_text.find("```", start)
            return response_text[start:end].strip()
        
        # 如果没有代码块，返回整个响应
        return response_text.strip()
    
    async def _analyze_test_failure(self, state: PlaywrightAgentState) -> Dict[str, Any]:
        """分析测试失败原因"""
        test_result = state["test_result"]
        
        prompt = DEBUG_ERROR_PROMPT.format(
            failed_code=state["generated_code"],
            error_message=test_result.error_message,
            execution_logs="\n".join(test_result.logs),
            page_state="未知",
            console_logs="无"
        )
        
        messages = [
            SystemMessage(content="你是一个Playwright测试调试专家。"),
            HumanMessage(content=prompt)
        ]
        
        response = await self.llm.ainvoke(messages)
        
        try:
            # 解析JSON响应
            if "```json" in response.content:
                json_start = response.content.find("```json") + 7
                json_end = response.content.find("```", json_start)
                json_text = response.content[json_start:json_end].strip()
                return json.loads(json_text)
        except:
            pass
        
        # 如果解析失败，返回基本错误信息
        return {
            "error_category": "unknown",
            "failure_point": "测试执行",
            "probable_causes": [{"cause": test_result.error_message, "likelihood": "高", "evidence": "错误消息"}],
            "recommended_fixes": [],
            "retry_recommended": state["retry_count"] < 2
        }
    
    async def _get_fix_suggestions(self, state: PlaywrightAgentState) -> List[Dict[str, Any]]:
        """获取修复建议"""
        error_diagnosis = state["error_diagnosis"]
        return error_diagnosis.get("recommended_fixes", [])
    
    async def _apply_fixes(self, state: PlaywrightAgentState, fix_suggestions: List[Dict[str, Any]]) -> str:
        """应用修复建议"""
        current_code = state["generated_code"]
        
        # 应用第一个修复建议（简化实现）
        if fix_suggestions:
            fix = fix_suggestions[0]
            code_change = fix.get("code_change", "")
            
            if code_change and code_change != current_code:
                return code_change
        
        return current_code
