"""代码验证工具"""

import re
import ast
import json
from typing import Dict, List, Any, Optional, Tuple


class CodeValidator:
    """代码验证器"""
    
    def __init__(self):
        self.playwright_patterns = self._load_playwright_patterns()
        self.best_practices = self._load_best_practices()
    
    def validate_playwright_code(self, code: str) -> Dict[str, Any]:
        """验证Playwright测试代码"""
        validation_result = {
            "is_valid": True,
            "syntax_errors": [],
            "logic_errors": [],
            "warnings": [],
            "suggestions": [],
            "best_practice_violations": [],
            "security_issues": [],
            "performance_issues": [],
            "score": 0
        }
        
        try:
            # 基础语法检查
            self._check_basic_syntax(code, validation_result)
            
            # Playwright特定检查
            self._check_playwright_specific(code, validation_result)
            
            # 逻辑检查
            self._check_logic_issues(code, validation_result)
            
            # 最佳实践检查
            self._check_best_practices(code, validation_result)
            
            # 性能检查
            self._check_performance_issues(code, validation_result)
            
            # 安全检查
            self._check_security_issues(code, validation_result)
            
            # 计算总分
            validation_result["score"] = self._calculate_score(validation_result)
            
            # 确定整体有效性
            validation_result["is_valid"] = (
                len(validation_result["syntax_errors"]) == 0 and
                len(validation_result["logic_errors"]) == 0 and
                validation_result["score"] >= 60
            )
            
        except Exception as e:
            validation_result["syntax_errors"].append(f"验证过程出错: {str(e)}")
            validation_result["is_valid"] = False
        
        return validation_result
    
    def _check_basic_syntax(self, code: str, result: Dict[str, Any]):
        """检查基础语法"""
        # 检查基本的JavaScript语法结构
        required_patterns = [
            (r"const\s+{\s*test\s*,\s*expect\s*}", "缺少Playwright导入语句"),
            (r"test\s*\(", "缺少测试函数定义"),
            (r"async\s+\(\s*{\s*page\s*}\s*\)", "缺少async page参数"),
        ]
        
        for pattern, error_msg in required_patterns:
            if not re.search(pattern, code):
                result["syntax_errors"].append(error_msg)
        
        # 检查括号匹配
        if not self._check_bracket_matching(code):
            result["syntax_errors"].append("括号不匹配")
        
        # 检查分号
        lines = code.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if line and not line.endswith((';', '{', '}', '//', '/*', '*/', ',')):
                if any(keyword in line for keyword in ['await', 'const', 'let', 'var']):
                    result["warnings"].append(f"第{i}行可能缺少分号")
    
    def _check_playwright_specific(self, code: str, result: Dict[str, Any]):
        """检查Playwright特定语法"""
        # 检查page对象使用
        page_methods = [
            'goto', 'click', 'fill', 'type', 'select', 'check', 'uncheck',
            'waitForSelector', 'waitForNavigation', 'waitForLoadState'
        ]
        
        for method in page_methods:
            if f"page.{method}" in code:
                # 检查是否使用await
                pattern = rf"(?<!await\s)page\.{method}\s*\("
                if re.search(pattern, code):
                    result["warnings"].append(f"page.{method}() 应该使用 await")
        
        # 检查选择器
        selector_patterns = [
            (r"page\.click\s*\(\s*['\"][^'\"]*['\"]", "click"),
            (r"page\.fill\s*\(\s*['\"][^'\"]*['\"]", "fill"),
            (r"page\.waitForSelector\s*\(\s*['\"][^'\"]*['\"]", "waitForSelector"),
        ]
        
        for pattern, method in selector_patterns:
            matches = re.findall(pattern, code)
            for match in matches:
                selector = re.search(r"['\"]([^'\"]*)['\"]", match)
                if selector:
                    self._validate_selector(selector.group(1), method, result)
        
        # 检查断言
        if "expect(" not in code:
            result["suggestions"].append("建议添加断言来验证测试结果")
        
        # 检查等待策略
        if "waitFor" not in code and "waitUntil" not in code:
            result["suggestions"].append("建议添加适当的等待机制")
    
    def _check_logic_issues(self, code: str, result: Dict[str, Any]):
        """检查逻辑问题"""
        # 检查页面导航
        if "page.goto" not in code:
            result["logic_errors"].append("缺少页面导航代码 (page.goto)")
        
        # 检查操作顺序
        operations = []
        for match in re.finditer(r"page\.(\w+)\s*\(", code):
            operations.append(match.group(1))
        
        if operations:
            # 检查是否在导航前进行操作
            if operations[0] != "goto" and operations[0] not in ["setViewportSize", "context"]:
                result["warnings"].append("建议先导航到页面再进行其他操作")
            
            # 检查点击后是否有验证
            for i, op in enumerate(operations):
                if op == "click" and i == len(operations) - 1:
                    result["suggestions"].append("点击操作后建议添加结果验证")
        
        # 检查异常处理
        if "try" not in code and "catch" not in code:
            result["suggestions"].append("建议添加异常处理机制")
    
    def _check_best_practices(self, code: str, result: Dict[str, Any]):
        """检查最佳实践"""
        # 检查硬编码等待
        if re.search(r"page\.waitForTimeout\s*\(\s*\d+", code):
            result["best_practice_violations"].append("避免使用硬编码等待时间，建议使用条件等待")
        
        # 检查选择器稳定性
        if re.search(r"nth-child\(\d+\)", code):
            result["warnings"].append("nth-child选择器可能不够稳定，建议使用更具体的标识")
        
        # 检查页面对象模式
        if len(code.split('\n')) > 50 and "class" not in code:
            result["suggestions"].append("复杂测试建议使用Page Object模式")
        
        # 检查数据驱动
        if code.count("fill(") > 3 and "test.each" not in code:
            result["suggestions"].append("多个相似操作建议使用数据驱动测试")
        
        # 检查测试独立性
        if "beforeEach" not in code and "afterEach" not in code:
            result["suggestions"].append("建议添加测试前后的清理操作")
    
    def _check_performance_issues(self, code: str, result: Dict[str, Any]):
        """检查性能问题"""
        # 检查并发操作
        promise_count = code.count("Promise.all")
        async_operations = len(re.findall(r"await\s+page\.", code))
        
        if async_operations > 5 and promise_count == 0:
            result["performance_issues"].append("多个异步操作建议使用Promise.all并发执行")
        
        # 检查资源加载
        if "waitForLoadState" not in code and "page.goto" in code:
            result["suggestions"].append("建议等待页面完全加载后再进行操作")
        
        # 检查元素定位效率
        xpath_count = code.count("xpath=")
        css_count = code.count("#") + code.count(".")
        
        if xpath_count > css_count:
            result["performance_issues"].append("XPath选择器性能较低，建议优先使用CSS选择器")
    
    def _check_security_issues(self, code: str, result: Dict[str, Any]):
        """检查安全问题"""
        # 检查敏感信息
        sensitive_patterns = [
            (r"password\s*=\s*['\"][^'\"]*['\"]", "代码中包含明文密码"),
            (r"token\s*=\s*['\"][^'\"]*['\"]", "代码中包含明文token"),
            (r"api[-_]?key\s*=\s*['\"][^'\"]*['\"]", "代码中包含明文API密钥"),
        ]
        
        for pattern, warning in sensitive_patterns:
            if re.search(pattern, code, re.IGNORECASE):
                result["security_issues"].append(warning)
        
        # 检查SQL注入风险
        if re.search(r"fill\s*\(\s*['\"][^'\"]*['\"]", code):
            result["suggestions"].append("注意输入验证，防止注入攻击")
    
    def _validate_selector(self, selector: str, method: str, result: Dict[str, Any]):
        """验证选择器"""
        # 检查选择器类型
        if selector.startswith("//"):
            result["suggestions"].append(f"XPath选择器 '{selector}' 可能不够稳定")
        elif ":nth-child(" in selector:
            result["warnings"].append(f"选择器 '{selector}' 使用nth-child可能不稳定")
        elif selector.startswith("#") or "data-testid" in selector:
            # 这些是好的选择器
            pass
        elif not any(char in selector for char in ["#", ".", "["]):
            result["warnings"].append(f"选择器 '{selector}' 可能过于简单，建议使用更具体的标识")
    
    def _check_bracket_matching(self, code: str) -> bool:
        """检查括号匹配"""
        stack = []
        brackets = {"(": ")", "[": "]", "{": "}"}
        
        in_string = False
        string_char = None
        
        for char in code:
            if char in ['"', "'"] and not in_string:
                in_string = True
                string_char = char
            elif char == string_char and in_string:
                in_string = False
                string_char = None
            elif not in_string:
                if char in brackets:
                    stack.append(char)
                elif char in brackets.values():
                    if not stack:
                        return False
                    if brackets[stack.pop()] != char:
                        return False
        
        return len(stack) == 0
    
    def _calculate_score(self, result: Dict[str, Any]) -> int:
        """计算代码质量分数"""
        score = 100
        
        # 扣分项
        score -= len(result["syntax_errors"]) * 20
        score -= len(result["logic_errors"]) * 15
        score -= len(result["security_issues"]) * 10
        score -= len(result["performance_issues"]) * 5
        score -= len(result["best_practice_violations"]) * 5
        score -= len(result["warnings"]) * 2
        
        return max(0, score)
    
    def _load_playwright_patterns(self) -> Dict[str, str]:
        """加载Playwright模式"""
        return {
            "navigation": r"page\.goto\s*\(['\"][^'\"]*['\"]\)",
            "click": r"page\.click\s*\(['\"][^'\"]*['\"]\)",
            "fill": r"page\.fill\s*\(['\"][^'\"]*['\"]\s*,\s*['\"][^'\"]*['\"]\)",
            "wait": r"page\.waitFor\w+\s*\(['\"][^'\"]*['\"]\)",
            "assertion": r"expect\s*\([^)]+\)\.",
        }
    
    def _load_best_practices(self) -> List[str]:
        """加载最佳实践规则"""
        return [
            "使用稳定的选择器（data-testid, id）",
            "避免硬编码等待时间",
            "添加适当的断言验证",
            "使用Page Object模式组织代码",
            "实现异常处理机制",
            "保持测试独立性",
            "优化异步操作性能"
        ]
    
    def suggest_improvements(self, code: str) -> List[Dict[str, str]]:
        """建议代码改进"""
        validation_result = self.validate_playwright_code(code)
        improvements = []
        
        # 根据验证结果生成改进建议
        for error in validation_result["syntax_errors"]:
            improvements.append({
                "type": "语法错误",
                "issue": error,
                "suggestion": self._get_syntax_fix_suggestion(error),
                "priority": "高"
            })
        
        for error in validation_result["logic_errors"]:
            improvements.append({
                "type": "逻辑错误",
                "issue": error,
                "suggestion": self._get_logic_fix_suggestion(error),
                "priority": "高"
            })
        
        for issue in validation_result["performance_issues"]:
            improvements.append({
                "type": "性能优化",
                "issue": issue,
                "suggestion": self._get_performance_fix_suggestion(issue),
                "priority": "中"
            })
        
        return improvements
    
    def _get_syntax_fix_suggestion(self, error: str) -> str:
        """获取语法修复建议"""
        if "导入语句" in error:
            return "添加: const { test, expect } = require('@playwright/test');"
        elif "测试函数" in error:
            return "添加: test('测试名称', async ({ page }) => { ... });"
        elif "async page" in error:
            return "确保测试函数参数为 async ({ page }) => { ... }"
        elif "括号" in error:
            return "检查并修复不匹配的括号"
        else:
            return "请检查代码语法"
    
    def _get_logic_fix_suggestion(self, error: str) -> str:
        """获取逻辑修复建议"""
        if "page.goto" in error:
            return "添加页面导航: await page.goto('https://example.com');"
        elif "导航前" in error:
            return "将 page.goto() 移到其他操作之前"
        else:
            return "请检查测试逻辑流程"
    
    def _get_performance_fix_suggestion(self, issue: str) -> str:
        """获取性能修复建议"""
        if "Promise.all" in issue:
            return "使用 Promise.all([...]) 并发执行多个异步操作"
        elif "XPath" in issue:
            return "将XPath选择器替换为CSS选择器或data-testid"
        else:
            return "优化异步操作和等待策略"
