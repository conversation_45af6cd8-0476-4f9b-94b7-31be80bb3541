"""DOM分析工具"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
# 注意：需要安装 beautifulsoup4: pip install beautifulsoup4
try:
    from bs4 import BeautifulSoup
except ImportError:
    print("警告：BeautifulSoup4 未安装，请运行: pip install beautifulsoup4")
    BeautifulSoup = None
from src.models.schemas import ElementInfo


class DOMAnalyzer:
    """DOM结构分析器"""
    
    def __init__(self):
        self.soup = None
        self.element_cache = {}
    
    def parse_dom(self, dom_html: str) -> Dict[str, Any]:
        """解析DOM结构"""
        try:
            if BeautifulSoup is None:
                return {
                    "error": "BeautifulSoup4 未安装，请运行: pip install beautifulsoup4",
                    "structure": {},
                    "forms": [],
                    "interactive_elements": [],
                    "navigation_elements": [],
                    "content_elements": [],
                    "suggested_selectors": {}
                }
            
            self.soup = BeautifulSoup(dom_html, 'html.parser')
            
            analysis_result = {
                "structure": self._analyze_structure(),
                "forms": self._find_forms(),
                "interactive_elements": self._find_interactive_elements(),
                "navigation_elements": self._find_navigation_elements(),
                "content_elements": self._find_content_elements(),
                "suggested_selectors": self._generate_selectors()
            }
            
            return analysis_result
            
        except Exception as e:
            return {
                "error": f"DOM解析失败: {str(e)}",
                "structure": {},
                "forms": [],
                "interactive_elements": [],
                "navigation_elements": [],
                "content_elements": [],
                "suggested_selectors": {}
            }
    
    def _analyze_structure(self) -> Dict[str, Any]:
        """分析页面结构"""
        if not self.soup:
            return {}
        
        structure = {
            "title": self.soup.title.string if self.soup.title else "无标题",
            "meta_info": self._extract_meta_info(),
            "main_sections": self._identify_main_sections(),
            "depth": self._calculate_dom_depth(),
            "element_count": len(self.soup.find_all())
        }
        
        return structure
    
    def _extract_meta_info(self) -> Dict[str, str]:
        """提取meta信息"""
        meta_info = {}
        
        # 查找常用的meta标签
        meta_tags = self.soup.find_all('meta')
        for meta in meta_tags:
            name = meta.get('name') or meta.get('property')
            content = meta.get('content')
            if name and content:
                meta_info[name] = content
        
        return meta_info
    
    def _identify_main_sections(self) -> List[Dict[str, Any]]:
        """识别主要页面区域"""
        sections = []
        
        # 查找语义化标签
        semantic_tags = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer']
        
        for tag_name in semantic_tags:
            elements = self.soup.find_all(tag_name)
            for element in elements:
                section_info = {
                    "tag": tag_name,
                    "id": element.get('id'),
                    "class": element.get('class'),
                    "text_preview": self._get_text_preview(element),
                    "child_count": len(element.find_all())
                }
                sections.append(section_info)
        
        # 查找具有重要class或id的div
        important_divs = self.soup.find_all('div', {'class': re.compile(r'(container|content|main|wrapper|page)', re.I)})
        for div in important_divs:
            section_info = {
                "tag": "div",
                "id": div.get('id'),
                "class": div.get('class'),
                "text_preview": self._get_text_preview(div),
                "child_count": len(div.find_all())
            }
            sections.append(section_info)
        
        return sections
    
    def _calculate_dom_depth(self) -> int:
        """计算DOM深度"""
        def get_depth(element, current_depth=0):
            if not element.find_all():
                return current_depth
            return max(get_depth(child, current_depth + 1) for child in element.children if hasattr(child, 'find_all'))
        
        return get_depth(self.soup)
    
    def _find_forms(self) -> List[Dict[str, Any]]:
        """查找表单元素"""
        forms = []
        
        form_elements = self.soup.find_all('form')
        for form in form_elements:
            form_info = {
                "action": form.get('action'),
                "method": form.get('method', 'GET'),
                "id": form.get('id'),
                "class": form.get('class'),
                "inputs": self._analyze_form_inputs(form),
                "submit_buttons": self._find_submit_buttons(form)
            }
            forms.append(form_info)
        
        return forms
    
    def _analyze_form_inputs(self, form) -> List[Dict[str, Any]]:
        """分析表单输入元素"""
        inputs = []
        
        input_elements = form.find_all(['input', 'textarea', 'select'])
        for input_elem in input_elements:
            input_info = {
                "tag": input_elem.name,
                "type": input_elem.get('type', 'text'),
                "name": input_elem.get('name'),
                "id": input_elem.get('id'),
                "placeholder": input_elem.get('placeholder'),
                "required": input_elem.has_attr('required'),
                "label": self._find_associated_label(input_elem)
            }
            inputs.append(input_info)
        
        return inputs
    
    def _find_associated_label(self, input_elem) -> Optional[str]:
        """查找输入元素关联的标签"""
        input_id = input_elem.get('id')
        if input_id:
            label = self.soup.find('label', {'for': input_id})
            if label:
                return label.get_text(strip=True)
        
        # 查找父级label
        parent_label = input_elem.find_parent('label')
        if parent_label:
            return parent_label.get_text(strip=True)
        
        return None
    
    def _find_submit_buttons(self, form) -> List[Dict[str, Any]]:
        """查找提交按钮"""
        buttons = []
        
        # 查找input type="submit"
        submit_inputs = form.find_all('input', {'type': 'submit'})
        for submit_input in submit_inputs:
            button_info = {
                "tag": "input",
                "type": "submit",
                "value": submit_input.get('value', 'Submit'),
                "id": submit_input.get('id'),
                "class": submit_input.get('class')
            }
            buttons.append(button_info)
        
        # 查找button标签
        button_elements = form.find_all('button')
        for button in button_elements:
            button_info = {
                "tag": "button",
                "type": button.get('type', 'button'),
                "text": button.get_text(strip=True),
                "id": button.get('id'),
                "class": button.get('class')
            }
            buttons.append(button_info)
        
        return buttons
    
    def _find_interactive_elements(self) -> List[Dict[str, Any]]:
        """查找交互元素"""
        interactive = []
        
        # 查找按钮
        buttons = self.soup.find_all(['button', 'input'])
        for button in buttons:
            if button.name == 'input' and button.get('type') not in ['button', 'submit', 'reset']:
                continue
            
            element_info = {
                "tag": button.name,
                "type": button.get('type'),
                "text": button.get_text(strip=True) or button.get('value', ''),
                "id": button.get('id'),
                "class": button.get('class'),
                "onclick": button.get('onclick'),
                "selector_suggestions": self._generate_element_selectors(button)
            }
            interactive.append(element_info)
        
        # 查找链接
        links = self.soup.find_all('a')
        for link in links:
            if link.get('href'):
                element_info = {
                    "tag": "a",
                    "href": link.get('href'),
                    "text": link.get_text(strip=True),
                    "id": link.get('id'),
                    "class": link.get('class'),
                    "target": link.get('target'),
                    "selector_suggestions": self._generate_element_selectors(link)
                }
                interactive.append(element_info)
        
        return interactive
    
    def _find_navigation_elements(self) -> List[Dict[str, Any]]:
        """查找导航元素"""
        navigation = []
        
        # 查找nav标签
        nav_elements = self.soup.find_all('nav')
        for nav in nav_elements:
            nav_info = {
                "tag": "nav",
                "id": nav.get('id'),
                "class": nav.get('class'),
                "links": [{"text": a.get_text(strip=True), "href": a.get('href')} 
                         for a in nav.find_all('a') if a.get('href')]
            }
            navigation.append(nav_info)
        
        # 查找菜单相关的元素
        menu_elements = self.soup.find_all(['ul', 'ol'], {'class': re.compile(r'(menu|nav)', re.I)})
        for menu in menu_elements:
            menu_info = {
                "tag": menu.name,
                "id": menu.get('id'),
                "class": menu.get('class'),
                "items": [li.get_text(strip=True) for li in menu.find_all('li')]
            }
            navigation.append(menu_info)
        
        return navigation
    
    def _find_content_elements(self) -> List[Dict[str, Any]]:
        """查找内容元素"""
        content = []
        
        # 查找标题
        headings = self.soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        for heading in headings:
            content_info = {
                "type": "heading",
                "tag": heading.name,
                "text": heading.get_text(strip=True),
                "id": heading.get('id'),
                "class": heading.get('class')
            }
            content.append(content_info)
        
        # 查找重要的文本区域
        text_elements = self.soup.find_all(['p', 'div'], string=True)
        for element in text_elements[:10]:  # 限制数量
            text = element.get_text(strip=True)
            if len(text) > 20:  # 只保留有意义的文本
                content_info = {
                    "type": "text",
                    "tag": element.name,
                    "text_preview": text[:100] + "..." if len(text) > 100 else text,
                    "id": element.get('id'),
                    "class": element.get('class')
                }
                content.append(content_info)
        
        return content
    
    def _generate_selectors(self) -> Dict[str, List[str]]:
        """生成推荐的选择器"""
        selectors = {
            "id_selectors": [],
            "class_selectors": [],
            "attribute_selectors": [],
            "text_selectors": [],
            "css_selectors": []
        }
        
        # ID选择器
        id_elements = self.soup.find_all(id=True)
        for element in id_elements:
            selector = f"#{element.get('id')}"
            selectors["id_selectors"].append(selector)
        
        # Class选择器
        class_elements = self.soup.find_all(class_=True)
        for element in class_elements:
            classes = element.get('class')
            if isinstance(classes, list):
                for cls in classes:
                    selector = f".{cls}"
                    if selector not in selectors["class_selectors"]:
                        selectors["class_selectors"].append(selector)
        
        # 属性选择器
        testid_elements = self.soup.find_all(attrs={"data-testid": True})
        for element in testid_elements:
            selector = f"[data-testid='{element.get('data-testid')}']"
            selectors["attribute_selectors"].append(selector)
        
        return selectors
    
    def _generate_element_selectors(self, element) -> List[str]:
        """为特定元素生成选择器建议"""
        selectors = []
        
        # ID选择器（最优先）
        if element.get('id'):
            selectors.append(f"#{element.get('id')}")
        
        # data-testid选择器
        if element.get('data-testid'):
            selectors.append(f"[data-testid='{element.get('data-testid')}']")
        
        # Class选择器
        if element.get('class'):
            classes = element.get('class')
            if isinstance(classes, list):
                selectors.append(f".{'.'.join(classes)}")
        
        # 文本选择器（适用于链接和按钮）
        text = element.get_text(strip=True)
        if text and element.name in ['a', 'button']:
            selectors.append(f"text='{text}'")
        
        # 属性选择器
        if element.get('type'):
            selectors.append(f"input[type='{element.get('type')}']")
        
        # CSS选择器
        if element.name:
            selectors.append(element.name)
        
        return selectors
    
    def _get_text_preview(self, element, max_length: int = 50) -> str:
        """获取元素文本预览"""
        text = element.get_text(strip=True)
        if len(text) > max_length:
            return text[:max_length] + "..."
        return text
    
    def find_best_selector(self, element_description: str, element_type: str = None) -> List[ElementInfo]:
        """根据描述查找最佳选择器"""
        candidates = []
        
        if not self.soup:
            return candidates
        
        # 根据描述关键词搜索
        keywords = element_description.lower().split()
        
        # 搜索包含关键词的元素
        for keyword in keywords:
            # 搜索文本内容
            text_elements = self.soup.find_all(string=re.compile(keyword, re.I))
            for text_elem in text_elements:
                parent = text_elem.parent
                if parent and parent.name not in ['script', 'style']:
                    element_info = self._create_element_info(parent, f"text='{text_elem.strip()}'")
                    candidates.append(element_info)
            
            # 搜索属性值
            attr_elements = self.soup.find_all(attrs={"id": re.compile(keyword, re.I)})
            attr_elements.extend(self.soup.find_all(attrs={"class": re.compile(keyword, re.I)}))
            attr_elements.extend(self.soup.find_all(attrs={"data-testid": re.compile(keyword, re.I)}))
            
            for element in attr_elements:
                selectors = self._generate_element_selectors(element)
                if selectors:
                    element_info = self._create_element_info(element, selectors[0])
                    candidates.append(element_info)
        
        # 去重并排序
        unique_candidates = []
        seen_selectors = set()
        
        for candidate in candidates:
            if candidate.selector not in seen_selectors:
                unique_candidates.append(candidate)
                seen_selectors.add(candidate.selector)
        
        return unique_candidates[:5]  # 返回前5个候选
    
    def _create_element_info(self, element, selector: str) -> ElementInfo:
        """创建元素信息对象"""
        # 确定选择器类型
        selector_type = "css"
        if selector.startswith("#"):
            selector_type = "id"
        elif selector.startswith("."):
            selector_type = "class"
        elif selector.startswith("text="):
            selector_type = "text"
        elif selector.startswith("["):
            selector_type = "attribute"
        
        # 确定元素类型
        element_type = element.name
        if element.name == "input":
            element_type = element.get("type", "input")
        
        # 确定操作类型
        action = "click"
        if element.name == "input":
            input_type = element.get("type", "text")
            if input_type in ["text", "password", "email", "number"]:
                action = "fill"
            elif input_type in ["checkbox", "radio"]:
                action = "check"
        elif element.name == "select":
            action = "select"
        elif element.name == "a":
            action = "click"
        
        return ElementInfo(
            selector=selector,
            selector_type=selector_type,
            element_type=element_type,
            action=action,
            value=None
        )
