"""Playwright测试执行器"""

import asyncio
import subprocess
import json
import os
import time
import tempfile
from typing import Dict, Any, Optional, List
from pathlib import Path

from src.models.schemas import TestResult, PlaywrightConfig


class PlaywrightRunner:
    """Playwright测试执行器"""
    
    def __init__(self, config: PlaywrightConfig):
        self.config = config
        self.temp_dir = tempfile.mkdtemp(prefix="playwright_tests_")
        
    async def execute_test(self, test_code: str, test_name: str = "generated_test") -> TestResult:
        """执行Playwright测试代码"""
        try:
            # 创建临时测试文件
            test_file = await self._create_test_file(test_code, test_name)
            
            # 执行测试
            start_time = time.time()
            result = await self._run_playwright_test(test_file)
            execution_time = time.time() - start_time
            
            # 分析结果
            success = result.returncode == 0
            error_message = result.stderr.decode() if not success else None
            logs = result.stdout.decode().split('\n')
            
            # 查找截图文件
            screenshot_path = await self._find_screenshot(test_name)
            
            return TestResult(
                success=success,
                error_message=error_message,
                execution_time=execution_time,
                screenshot_path=screenshot_path,
                logs=logs
            )
            
        except Exception as e:
            return TestResult(
                success=False,
                error_message=str(e),
                execution_time=0.0,
                logs=[f"执行器错误: {str(e)}"]
            )
    
    async def _create_test_file(self, test_code: str, test_name: str) -> str:
        """创建临时测试文件"""
        test_file_path = os.path.join(self.temp_dir, f"{test_name}.spec.js")
        
        # 添加必要的配置和导入
        full_test_code = self._wrap_test_code(test_code)
        
        with open(test_file_path, 'w', encoding='utf-8') as f:
            f.write(full_test_code)
            
        return test_file_path
    
    def _wrap_test_code(self, test_code: str) -> str:
        """包装测试代码，添加必要的配置"""
        # 如果代码已经包含导入语句，直接返回
        if "import {" in test_code or "const {" in test_code:
            return test_code
        
        # 否则添加基本的导入和配置
        import_line = "import { test, expect } from '@playwright/test';"
        config_code = f"""
{import_line}

// 全局配置
test.use({{
  headless: {str(self.config.headless).lower()},
  viewport: {{ width: {self.config.viewport_width}, height: {self.config.viewport_height} }},
  actionTimeout: {self.config.timeout},
  navigationTimeout: {self.config.timeout}
}});

{test_code}
"""
        return config_code
    
    async def _run_playwright_test(self, test_file: str) -> subprocess.CompletedProcess:
        """运行Playwright测试"""
        # 执行命令 - 从项目根目录运行以使用 playwright.config.js
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        
        cmd = [
            "npx", "playwright", "test", 
            test_file,
            "--reporter=json",
            f"--timeout={self.config.timeout}"
        ]
        
        # 添加浏览器类型
        if hasattr(self.config, 'browser_type'):
            cmd.extend(["--project", self.config.browser_type])
        
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=project_root
        )
        
        stdout, stderr = await process.communicate()
        
        return subprocess.CompletedProcess(
            args=cmd,
            returncode=process.returncode,
            stdout=stdout,
            stderr=stderr
        )
    
    async def _find_screenshot(self, test_name: str) -> Optional[str]:
        """查找测试截图文件"""
        screenshot_patterns = [
            f"test-results/*{test_name}*/screenshot.png",
            f"test-results/*/screenshot.png",
            "screenshots/*.png"
        ]
        
        for pattern in screenshot_patterns:
            screenshot_path = os.path.join(self.temp_dir, pattern)
            if os.path.exists(screenshot_path):
                return screenshot_path
        
        return None
    
    async def validate_test_code(self, test_code: str) -> Dict[str, Any]:
        """验证测试代码语法和结构"""
        validation_result = {
            "is_valid": True,
            "errors": [],
            "warnings": [],
            "suggestions": []
        }
        
        try:
            # 基本语法检查
            if "const { test, expect }" not in test_code and "import { test, expect }" not in test_code:
                validation_result["warnings"].append("缺少Playwright测试导入语句")
            
            if "test(" not in test_code:
                validation_result["errors"].append("缺少测试函数定义")
                validation_result["is_valid"] = False
            
            if "page.goto" not in test_code:
                validation_result["warnings"].append("建议添加页面导航代码")
            
            # 检查async/await使用
            if "async" not in test_code:
                validation_result["warnings"].append("建议使用async/await语法")
            
            # 检查断言
            if "expect(" not in test_code:
                validation_result["suggestions"].append("建议添加断言验证")
            
            # 检查等待机制
            if "waitFor" not in test_code and "waitUntil" not in test_code:
                validation_result["suggestions"].append("建议添加适当的等待机制")
            
        except Exception as e:
            validation_result["errors"].append(f"代码验证失败: {str(e)}")
            validation_result["is_valid"] = False
        
        return validation_result
    
    def cleanup(self):
        """清理临时文件"""
        try:
            import shutil
            shutil.rmtree(self.temp_dir, ignore_errors=True)
        except Exception:
            pass
    
    def __del__(self):
        """析构函数，自动清理"""
        self.cleanup()


class PlaywrightInstaller:
    """Playwright安装和配置管理器"""
    
    @staticmethod
    async def check_installation() -> Dict[str, Any]:
        """检查Playwright安装状态"""
        result = {
            "playwright_installed": False,
            "browsers_installed": False,
            "version": None,
            "missing_dependencies": []
        }
        
        try:
            # 检查Playwright是否安装
            process = await asyncio.create_subprocess_exec(
                "npx", "playwright", "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                result["playwright_installed"] = True
                result["version"] = stdout.decode().strip()
            else:
                result["missing_dependencies"].append("Playwright未安装")
            
            # 检查浏览器是否安装
            if result["playwright_installed"]:
                browser_check = await asyncio.create_subprocess_exec(
                    "npx", "playwright", "install", "--dry-run",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                browser_stdout, browser_stderr = await browser_check.communicate()
                
                if "already installed" in browser_stdout.decode().lower():
                    result["browsers_installed"] = True
                else:
                    result["missing_dependencies"].append("浏览器未安装")
            
        except Exception as e:
            result["missing_dependencies"].append(f"检查失败: {str(e)}")
        
        return result
    
    @staticmethod
    async def install_playwright() -> Dict[str, Any]:
        """安装Playwright和浏览器"""
        result = {
            "success": False,
            "message": "",
            "details": []
        }
        
        try:
            # 安装Playwright
            result["details"].append("开始安装Playwright...")
            
            install_process = await asyncio.create_subprocess_exec(
                "npm", "install", "@playwright/test",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await install_process.communicate()
            
            if install_process.returncode == 0:
                result["details"].append("Playwright安装成功")
                
                # 安装浏览器
                result["details"].append("开始安装浏览器...")
                
                browser_process = await asyncio.create_subprocess_exec(
                    "npx", "playwright", "install",
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                browser_stdout, browser_stderr = await browser_process.communicate()
                
                if browser_process.returncode == 0:
                    result["success"] = True
                    result["message"] = "Playwright和浏览器安装成功"
                    result["details"].append("浏览器安装成功")
                else:
                    result["message"] = f"浏览器安装失败: {browser_stderr.decode()}"
                    result["details"].append(f"浏览器安装错误: {browser_stderr.decode()}")
            else:
                result["message"] = f"Playwright安装失败: {stderr.decode()}"
                result["details"].append(f"安装错误: {stderr.decode()}")
                
        except Exception as e:
            result["message"] = f"安装过程出错: {str(e)}"
            result["details"].append(f"异常: {str(e)}")
        
        return result
