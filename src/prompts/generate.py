"""代码生成相关的提示词模板"""

GENERATE_CODE_PROMPT = """
你是一个专业的Playwright测试代码生成专家。请根据分析结果生成高质量的Playwright测试代码。

## 输入信息
- 测试场景：{scenario}
- 测试步骤：{test_steps}
- 目标元素：{target_elements}
- 现有代码：{existing_code}
- 配置信息：{config}

## 代码生成要求
1. **代码结构**：
   - 使用现代的async/await语法
   - 合理的函数组织和模块化
   - 清晰的注释和文档

2. **元素定位**：
   - 优先使用稳定的定位策略
   - 添加适当的等待机制
   - 处理动态加载内容

3. **错误处理**：
   - 添加try-catch异常处理
   - 提供有意义的错误信息
   - 实现优雅的降级机制

4. **最佳实践**：
   - 使用Page Object模式（如适用）
   - 避免硬编码等待时间
   - 添加断言验证

## 代码模板
请参考以下模板结构生成测试代码：

```javascript
import {{ test, expect }} from '@playwright/test';

test('测试名称', async ({{ page }}) => {{
  // 测试设置
  await page.setViewportSize({{ width: 1280, height: 720 }});
  
  // 导航到页面
  await page.goto('https://example.com');
  
  // 测试步骤
  // 在这里添加具体的测试步骤
  
  // 断言验证
  // 在这里添加必要的断言
}});
```

## 输出要求
请只生成纯 JavaScript 代码，用 ```javascript 代码块包围，确保：
- 代码语法正确且可执行
- 包含所有必要的导入和设置
- 添加适当的注释说明
- 遵循Playwright最佳实践
- 不要包含任何JSON或其他格式，只要纯JavaScript代码

示例输出格式：
```javascript
import {{ test, expect }} from '@playwright/test';

test('测试名称', async ({{ page }}) => {{
  // 你的测试代码
}});
```

生成的代码应该直接可以保存为.spec.js文件并运行。
"""

MODIFY_CODE_PROMPT = """
你是一个Playwright测试代码修改专家。请根据需求对现有代码进行智能修改。

## 修改信息
- 原始代码：{original_code}
- 修改需求：{modification_requirements}
- 错误信息：{error_info}
- DOM变化：{dom_changes}

## 修改策略
1. **增量修改**：
   - 最小化修改范围
   - 保持原有代码结构
   - 避免破坏性改动

2. **问题修复**：
   - 针对性解决具体问题
   - 提升代码健壮性
   - 增强错误处理

3. **功能增强**：
   - 添加新的测试步骤
   - 改进元素定位
   - 优化等待策略

## 修改类型
根据需求选择修改类型：
- **元素定位修改**：更新选择器或定位策略
- **逻辑流程修改**：调整测试步骤顺序或条件
- **断言修改**：更新验证条件或预期结果
- **配置修改**：调整超时、视窗等设置
- **错误修复**：解决语法或逻辑错误

## 输出要求
请提供：
1. **修改后的完整代码**
2. **修改说明**：详细描述做了哪些修改
3. **修改原因**：解释为什么要这样修改
4. **注意事项**：使用时需要注意的问题

格式示例：
```json
{{
  "modified_code": "修改后的完整代码",
  "changes_made": [
    {{
      "line_range": "行号范围",
      "description": "修改描述",
      "reason": "修改原因"
    }}
  ],
  "improvement_summary": "整体改进总结",
  "usage_notes": ["注意事项1", "注意事项2"]
}}
```
"""

CODE_TEMPLATES = {
    "basic_test": """
const { test, expect } = require('@playwright/test');

test('{test_name}', async ({ page }) => {
  // 设置视窗大小
  await page.setViewportSize({ width: 1280, height: 720 });
  
  // 导航到页面
  await page.goto('{url}');
  
  // 等待页面加载完成
  await page.waitForLoadState('networkidle');
  
  {test_steps}
});
""",
    
    "form_interaction": """
// 表单交互测试
await page.fill('{input_selector}', '{input_value}');
await page.click('{submit_selector}');
await expect(page.locator('{result_selector}')).toBeVisible();
""",
    
    "navigation_test": """
// 页面导航测试
await page.click('{link_selector}');
await page.waitForURL('{expected_url}');
await expect(page).toHaveTitle('{expected_title}');
""",
    
    "element_interaction": """
// 元素交互测试
await page.waitForSelector('{element_selector}');
await page.click('{element_selector}');
await expect(page.locator('{result_selector}')).toHaveText('{expected_text}');
""",
    
    "api_wait": """
// 等待API响应
await page.waitForResponse(response => 
  response.url().includes('{api_endpoint}') && response.status() === 200
);
"""
}
