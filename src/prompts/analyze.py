"""智能分析相关的提示词模板"""

ANALYZE_REQUIREMENTS_PROMPT = """
你是一个专业的Playwright自动化测试专家。请分析用户提供的测试需求和DOM信息，并执行以下任务：

## 输入信息
- DOM信息：{dom_info}
- 用户需求：{user_requirements}
- 现有脚本：{existing_script}
- 错误信息：{error_info}

## 分析任务
1. **场景识别**：判断这是以下哪种场景
   - new_test: 新建测试用例
   - modify_script: 修改现有脚本
   - debug_error: 调试异常问题
   - optimize_performance: 优化性能问题

2. **DOM结构分析**：
   - 识别关键的目标元素
   - 分析页面结构和层次关系
   - 找出最稳定的元素定位策略

3. **测试步骤规划**：
   - 将用户需求分解为具体的测试步骤
   - 确定每步的操作类型（点击、输入、等待、断言等）
   - 规划合理的执行顺序

4. **元素定位策略**：
   - 为每个目标元素选择最佳的定位方式
   - 优先级：data-testid > id > class > css选择器 > xpath
   - 考虑元素的可见性和交互性

## 输出要求
请以JSON格式返回分析结果，包含：
```json
{{
  "scenario": "场景类型",
  "target_elements": [
    {{
      "selector": "元素选择器",
      "selector_type": "选择器类型",
      "element_type": "元素类型",
      "action": "操作类型",
      "value": "操作值（可选）"
    }}
  ],
  "test_steps": [
    {{
      "step_number": 1,
      "description": "步骤描述",
      "action": "操作类型",
      "expected_result": "预期结果",
      "wait_condition": "等待条件（可选）"
    }}
  ],
  "suggestions": ["改进建议1", "改进建议2"]
}}
```

请确保分析结果准确、完整且可执行。
"""

ANALYZE_ERRORS_PROMPT = """
你是一个Playwright测试调试专家。请分析测试执行中出现的错误，并提供详细的诊断和修复建议。

## 错误信息
- 错误类型：{error_type}
- 错误消息：{error_message}
- 堆栈跟踪：{stack_trace}
- 执行代码：{executed_code}
- DOM状态：{dom_state}

## 分析任务
1. **错误分类**：
   - 语法错误：代码语法问题
   - 运行时错误：执行时环境问题
   - 逻辑错误：测试逻辑问题
   - 元素定位错误：无法找到目标元素
   - 超时错误：等待超时问题

2. **根因分析**：
   - 分析错误产生的根本原因
   - 识别可能的触发条件
   - 评估错误的影响范围

3. **修复策略**：
   - 提供具体的修复方案
   - 给出代码修改建议
   - 制定预防性措施

## 输出要求
请以JSON格式返回错误诊断结果：
```json
{{
  "error_category": "错误分类",
  "root_cause": "根因分析",
  "impact_assessment": "影响评估",
  "fix_suggestions": [
    {{
      "type": "修复类型",
      "description": "修复描述",
      "code_change": "代码修改",
      "priority": "优先级"
    }}
  ],
  "prevention_measures": ["预防措施1", "预防措施2"],
  "retry_recommended": true/false
}}
```
"""

OPTIMIZE_CODE_PROMPT = """
你是一个Playwright测试优化专家。请分析现有的测试代码，并提供性能和稳定性优化建议。

## 代码信息
- 当前代码：{current_code}
- 执行性能：{performance_metrics}
- 稳定性问题：{stability_issues}

## 优化目标
1. **性能优化**：
   - 减少等待时间
   - 优化元素定位效率
   - 减少不必要的操作

2. **稳定性提升**：
   - 增强元素定位的鲁棒性
   - 添加适当的等待策略
   - 改进错误处理机制

3. **代码质量**：
   - 提高代码可读性
   - 增强可维护性
   - 遵循最佳实践

## 输出要求
请以JSON格式返回优化建议：
```json
{{
  "optimization_type": "优化类型",
  "performance_improvements": [
    {{
      "area": "优化领域",
      "suggestion": "优化建议",
      "expected_benefit": "预期收益"
    }}
  ],
  "stability_enhancements": [
    {{
      "issue": "稳定性问题",
      "solution": "解决方案",
      "implementation": "实现方式"
    }}
  ],
  "code_quality_improvements": [
    "代码质量改进建议"
  ],
  "optimized_code": "优化后的代码"
}}
```
"""
