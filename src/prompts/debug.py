"""调试相关的提示词模板"""

DEBUG_ERROR_PROMPT = """
你是一个Playwright测试调试专家。请分析测试执行失败的原因并提供详细的调试信息。

## 错误上下文
- 失败的测试代码：{failed_code}
- 错误消息：{error_message}
- 执行日志：{execution_logs}
- 页面状态：{page_state}
- 浏览器控制台：{console_logs}

## 调试分析
1. **错误定位**：
   - 确定具体失败的代码行
   - 分析错误发生的上下文
   - 识别相关的页面状态

2. **原因分析**：
   - 元素定位问题：selector失效、元素不存在
   - 时序问题：页面未完全加载、异步操作
   - 数据问题：输入数据格式错误、状态不符合预期
   - 环境问题：网络延迟、浏览器兼容性

3. **调试策略**：
   - 添加调试输出
   - 增加等待条件
   - 改进错误处理
   - 优化定位策略

## 常见问题模式
1. **元素未找到**：
   ```
   Error: Timeout 30000ms exceeded.
   =========================== logs ===========================
   waiting for selector "button" to be visible
   ```
   
2. **点击失败**：
   ```
   Error: element is not clickable at point (x, y)
   ```
   
3. **断言失败**：
   ```
   Error: expect(received).toBe(expected)
   Expected: "success"
   Received: "error"
   ```

## 输出要求
请提供详细的调试分析：
```json
{{
  "error_category": "错误分类",
  "failure_point": "具体失败位置",
  "probable_causes": [
    {{
      "cause": "可能原因",
      "likelihood": "可能性（高/中/低）",
      "evidence": "支持证据"
    }}
  ],
  "debugging_steps": [
    {{
      "step": "调试步骤",
      "purpose": "目的",
      "code_example": "示例代码"
    }}
  ],
  "recommended_fixes": [
    {{
      "fix_type": "修复类型",
      "description": "修复描述",
      "code_change": "代码修改",
      "test_approach": "测试方法"
    }}
  ],
  "prevention_tips": ["预防建议1", "预防建议2"]
}}
```
"""

PERFORMANCE_DEBUG_PROMPT = """
你是一个Playwright性能调试专家。请分析测试执行性能问题并提供优化建议。

## 性能数据
- 执行时间：{execution_time}ms
- 页面加载时间：{page_load_time}ms
- 网络请求：{network_requests}
- 资源加载：{resource_loading}
- 元素查找时间：{element_search_time}

## 性能分析
1. **性能瓶颈识别**：
   - 页面加载慢：网络请求、资源大小
   - 元素定位慢：选择器效率、DOM复杂度
   - 操作执行慢：等待策略、同步问题

2. **优化机会**：
   - 并行化操作
   - 优化等待策略
   - 减少不必要的请求
   - 改进元素定位

3. **性能基准**：
   - 页面加载：< 3秒
   - 元素定位：< 1秒
   - 用户操作：< 500ms

## 输出要求
```json
{{
  "performance_summary": {{
    "total_time": "总执行时间",
    "bottlenecks": ["性能瓶颈1", "性能瓶颈2"],
    "efficiency_score": "效率评分（1-10）"
  }},
  "optimization_suggestions": [
    {{
      "area": "优化领域",
      "current_issue": "当前问题",
      "suggested_solution": "建议解决方案",
      "expected_improvement": "预期改进效果"
    }}
  ],
  "code_optimizations": [
    {{
      "original_code": "原始代码",
      "optimized_code": "优化代码",
      "improvement_reason": "改进原因"
    }}
  ]
}}
```
"""

STABILITY_DEBUG_PROMPT = """
你是一个Playwright测试稳定性专家。请分析测试的稳定性问题并提供改进方案。

## 稳定性问题
- 失败率：{failure_rate}%
- 常见失败原因：{common_failures}
- 环境变化：{environment_changes}
- 测试波动性：{test_flakiness}

## 稳定性分析
1. **不稳定因素**：
   - 网络延迟和波动
   - 页面加载时序
   - 动态内容变化
   - 外部依赖服务

2. **稳定性模式**：
   - 间歇性失败
   - 环境相关失败
   - 数据依赖失败
   - 时序相关失败

3. **改进策略**：
   - 增强等待机制
   - 添加重试逻辑
   - 改进元素定位
   - 减少外部依赖

## 输出要求
```json
{{
  "stability_assessment": {{
    "overall_score": "整体稳定性评分（1-10）",
    "main_issues": ["主要问题1", "主要问题2"],
    "risk_factors": ["风险因素1", "风险因素2"]
  }},
  "improvement_plan": [
    {{
      "issue": "稳定性问题",
      "impact": "影响程度",
      "solution": "解决方案",
      "implementation": "实施方式",
      "priority": "优先级"
    }}
  ],
  "enhanced_code": "增强后的代码",
  "monitoring_suggestions": ["监控建议1", "监控建议2"]
}}
```
"""
