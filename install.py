#!/usr/bin/env python3
"""Playwright智能体安装脚本"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description}完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def check_node_version():
    """检查Node.js版本"""
    try:
        result = subprocess.run("node --version", shell=True, capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Node.js版本: {result.stdout.strip()}")
            return True
        else:
            print("❌ Node.js未安装，请先安装Node.js")
            return False
    except:
        print("❌ Node.js未安装，请先安装Node.js")
        return False


def install_python_dependencies():
    """安装Python依赖"""
    return run_command("pip install -r requirements.txt", "安装Python依赖")


def install_node_dependencies():
    """安装Node.js依赖"""
    commands = [
        ("npm init -y", "初始化npm项目"),
        ("npm install @playwright/test", "安装Playwright"),
        ("npx playwright install", "安装浏览器")
    ]
    
    for command, description in commands:
        if not run_command(command, description):
            return False
    return True


def create_directories():
    """创建必要目录"""
    directories = ["output", "logs", "screenshots"]
    for dir_name in directories:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"📁 创建目录: {dir_name}")
    return True


def setup_environment():
    """设置环境变量"""
    env_file = Path(".env")
    if not env_file.exists():
        env_content = """# LLM Configuration
OPENAI_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************.ED2fuIwehJ6fbi9PWGY7jgGCOKDGIJ19BbN4YEZHCjE
OPENAI_BASE_URL=https://muses.weizhipin.com/muses-open/openai/v1
OPENAI_MODEL=deepseek-v3-0324

# Playwright Configuration
PLAYWRIGHT_HEADLESS=false
PLAYWRIGHT_TIMEOUT=30000
"""
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✅ 创建.env配置文件")
    else:
        print("✅ .env文件已存在")
    return True


def run_test():
    """运行测试验证安装"""
    print("\n🧪 运行安装验证测试...")
    return run_command("python test/test_agent.py", "运行验证测试")


def main():
    """主安装流程"""
    print("🚀 Playwright智能体安装程序")
    print("=" * 50)
    
    # 检查系统要求
    if not check_python_version():
        sys.exit(1)
    
    if not check_node_version():
        print("请访问 https://nodejs.org 安装Node.js")
        sys.exit(1)
    
    # 安装步骤
    steps = [
        (install_python_dependencies, "安装Python依赖"),
        (install_node_dependencies, "安装Node.js依赖"),
        (create_directories, "创建目录结构"),
        (setup_environment, "设置环境配置"),
    ]
    
    for step_func, step_name in steps:
        print(f"\n📦 {step_name}...")
        if not step_func():
            print(f"❌ {step_name}失败，安装中止")
            sys.exit(1)
    
    print("\n🎉 安装完成！")
    print("\n📋 下一步操作:")
    print("  1. 检查并修改.env文件中的配置")
    print("  2. 运行示例: python main.py")
    print("  3. 交互模式: python main.py --interactive")
    print("  4. 查看帮助: python main.py --help")
    
    # 可选：运行验证测试
    choice = input("\n🧪 是否运行验证测试？(y/n): ").lower().strip()
    if choice in ['y', 'yes']:
        run_test()


if __name__ == "__main__":
    main()
