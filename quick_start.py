#!/usr/bin/env python3
"""Playwright智能体快速启动脚本"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from src.models.schemas import TestRequest
    from src.agent.graph import run_playwright_agent, get_agent_info
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


async def demo_login_test():
    """演示登录测试"""
    print("🔐 演示：登录表单测试")
    print("-" * 30)
    
    request = TestRequest(
        dom_info="""
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" name="username" data-testid="username-input" placeholder="请输入用户名">
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" data-testid="password-input" placeholder="请输入密码">
            </div>
            <button type="submit" id="loginBtn" data-testid="login-button">登录</button>
        </form>
        <div id="message" data-testid="login-message" style="display:none;"></div>
        """,
        user_requirements="测试用户登录功能：输入用户名'demo_user'和密码'demo_pass'，点击登录按钮，验证是否显示登录消息",
    )
    
    try:
        result = await run_playwright_agent(request)
        
        print(f"📊 执行结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        print(f"🎯 测试场景: {result.get('scenario', 'unknown')}")
        
        if result.get('generated_code'):
            print("\n📄 生成的测试代码:")
            print("```javascript")
            print(result['generated_code'])
            print("```")
        
        if result.get('suggestions'):
            print("\n💡 智能建议:")
            for suggestion in result['suggestions']:
                print(f"  • {suggestion}")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        return False


async def demo_shopping_cart():
    """演示购物车测试"""
    print("\n🛒 演示：购物车功能测试")
    print("-" * 30)
    
    request = TestRequest(
        dom_info="""
        <div class="product-list">
            <div class="product-item" data-id="1" data-testid="product-1">
                <h3>MacBook Pro</h3>
                <span class="price" data-testid="price-1">¥12999</span>
                <button class="add-to-cart" data-product-id="1" data-testid="add-cart-1">加入购物车</button>
            </div>
        </div>
        <div class="cart-widget" data-testid="cart-widget">
            <span>购物车 (<span class="cart-count" data-testid="cart-count">0</span>)</span>
        </div>
        """,
        user_requirements="测试添加商品到购物车：点击MacBook Pro的加入购物车按钮，验证购物车数量是否从0变为1",
    )
    
    try:
        result = await run_playwright_agent(request)
        
        print(f"📊 执行结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        
        if result.get('generated_code'):
            print("\n📄 生成的测试代码:")
            print("```javascript")
            print(result['generated_code'])
            print("```")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        return False


async def demo_code_fix():
    """演示代码修复"""
    print("\n🔧 演示：代码修复功能")
    print("-" * 30)
    
    request = TestRequest(
        dom_info="""<button id="submitButton" data-testid="submit-btn">提交</button>""",
        user_requirements="修复现有代码中的选择器错误",
        existing_script="""
        const { test, expect } = require('@playwright/test');
        
        test('提交测试', async ({ page }) => {
            await page.goto('https://example.com');
            await page.click('#submit');  // 错误的选择器
            await expect(page.locator('#result')).toBeVisible();
        });
        """,
        error_info="Error: Timeout 30000ms exceeded. waiting for selector '#submit' to be visible"
    )
    
    try:
        result = await run_playwright_agent(request)
        
        print(f"📊 修复结果: {'✅ 成功' if result['success'] else '❌ 失败'}")
        
        if result.get('generated_code'):
            print("\n📄 修复后的代码:")
            print("```javascript")
            print(result['generated_code'])
            print("```")
        
        if result.get('code_modifications'):
            print("\n🔄 修改记录:")
            for mod in result['code_modifications']:
                print(f"  • {mod}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复演示失败: {str(e)}")
        return False


def show_agent_info():
    """显示智能体信息"""
    print("🤖 Playwright自动化测试智能体")
    print("=" * 50)
    
    try:
        info = get_agent_info()
        print(f"📋 名称: {info['name']}")
        print(f"🔖 版本: {info['version']}")
        print(f"📝 描述: {info['description']}")
        
        print("\n🎯 核心能力:")
        capabilities = info['capabilities']
        for category, items in capabilities.items():
            print(f"\n  {category}:")
            for item in items:
                print(f"    • {item}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取智能体信息失败: {str(e)}")
        return False


async def run_quick_demos():
    """运行快速演示"""
    print("🚀 Playwright智能体快速演示")
    print("=" * 50)
    
    # 显示智能体信息
    if not show_agent_info():
        return False
    
    print(f"\n🎮 开始运行演示...")
    
    # 运行演示
    demos = [
        demo_login_test,
        demo_shopping_cart,
        demo_code_fix
    ]
    
    success_count = 0
    for demo in demos:
        try:
            if await demo():
                success_count += 1
        except Exception as e:
            print(f"❌ 演示出错: {str(e)}")
    
    print(f"\n📈 演示完成: {success_count}/{len(demos)} 个成功")
    
    if success_count == len(demos):
        print("🎉 所有演示都成功运行！智能体已就绪")
        print("\n🎯 接下来你可以:")
        print("  • 运行完整示例: python main.py")
        print("  • 使用交互模式: python main.py --interactive")
        print("  • 查看详细文档: cat README.md")
    else:
        print("⚠️ 部分演示失败，请检查环境配置")
        print("💡 常见问题:")
        print("  • 确保已安装所有依赖: pip install -r requirements.txt")
        print("  • 检查.env文件中的API配置")
        print("  • 确保网络连接正常")
    
    return success_count == len(demos)


def main():
    """主函数"""
    try:
        asyncio.run(run_quick_demos())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 运行失败: {str(e)}")
        print("💡 请尝试运行安装脚本: python install.py")


if __name__ == "__main__":
    main()
