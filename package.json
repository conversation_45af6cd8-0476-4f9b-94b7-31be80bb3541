{"name": "playwright-agent-tests", "version": "1.0.0", "description": "Generated Playwright tests by AI agent", "main": "index.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "report": "playwright show-report"}, "keywords": ["playwright", "testing", "automation", "ai"], "author": "Playwright Agent", "license": "MIT", "devDependencies": {"@playwright/test": "^1.55.0"}, "directories": {"doc": "docs", "test": "test"}, "dependencies": {"playwright": "^1.55.0", "playwright-core": "^1.55.0"}}