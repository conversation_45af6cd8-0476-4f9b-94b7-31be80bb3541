"""Playwright自动化测试智能体主程序入口"""

import asyncio
import json
from typing import Dict, Any, Optional
from pathlib import Path

from src.models.schemas import TestRequest
from src.agent.graph import run_playwright_agent, get_agent_info
from config import PLAYWRIGHT_HEADLESS, PLAYWRIGHT_TIMEOUT


async def main():
    """主程序入口"""
    print("🎯 Playwright自动化测试智能体")
    print("=" * 50)
    
    # 显示智能体信息
    agent_info = get_agent_info()
    print(f"📋 {agent_info['name']} v{agent_info['version']}")
    print(f"📝 {agent_info['description']}")
    print()
    
    # 运行示例测试
    await run_examples()


async def run_examples():
    """运行示例测试"""
    examples = [
        {
            "name": "登录表单测试",
            "request": TestRequest(
                dom_info="""
                <form id="loginForm">
                    <div>
                        <label for="username">用户名:</label>
                        <input type="text" id="username" name="username" placeholder="请输入用户名">
                    </div>
                    <div>
                        <label for="password">密码:</label>
                        <input type="password" id="password" name="password" placeholder="请输入密码">
                    </div>
                    <button type="submit" id="loginBtn">登录</button>
                </form>
                <div id="message" style="display:none;"></div>
                """,
                user_requirements="测试用户登录功能：输入用户名'admin'和密码'123456'，点击登录按钮，验证登录成功",
                existing_script=None,
                error_info=None
            )
        },
        {
            "name": "购物车操作测试",
            "request": TestRequest(
                dom_info="""
                <div class="product-list">
                    <div class="product-item" data-id="1">
                        <h3>商品A</h3>
                        <span class="price">¥99.00</span>
                        <button class="add-to-cart" data-product-id="1">加入购物车</button>
                    </div>
                    <div class="product-item" data-id="2">
                        <h3>商品B</h3>
                        <span class="price">¥199.00</span>
                        <button class="add-to-cart" data-product-id="2">加入购物车</button>
                    </div>
                </div>
                <div class="cart">
                    <span class="cart-count">0</span>
                    <a href="/cart" class="cart-link">查看购物车</a>
                </div>
                """,
                user_requirements="测试添加商品到购物车：点击商品A的加入购物车按钮，验证购物车数量增加",
                existing_script=None,
                error_info=None
            )
        },
        {
            "name": "代码修复示例",
            "request": TestRequest(
                dom_info="""
                <button id="submitBtn" class="btn-primary">提交</button>
                <div id="result"></div>
                """,
                user_requirements="修复现有测试代码中的元素定位问题",
                existing_script="""
                const { test, expect } = require('@playwright/test');
                
                test('提交测试', async ({ page }) => {
                    await page.goto('https://example.com');
                    await page.click('#submit'); // 错误的选择器
                    await expect(page.locator('#result')).toBeVisible();
                });
                """,
                error_info="Error: Timeout 30000ms exceeded. waiting for selector '#submit' to be visible"
            )
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n🔥 示例 {i}: {example['name']}")
        print("-" * 40)
        
        try:
            # 配置参数
            config = {
                "headless": PLAYWRIGHT_HEADLESS,
                "timeout": PLAYWRIGHT_TIMEOUT,
                "viewport_width": 1280,
                "viewport_height": 720
            }
            
            # 运行智能体
            result = await run_playwright_agent(example["request"], config)
            
            # 显示结果
            print_result(result)
            
            # 保存结果到文件
            save_result_to_file(f"example_{i}_{example['name']}", result)
            
        except Exception as e:
            print(f"❌ 示例运行失败: {str(e)}")
        
        print("\n" + "=" * 50)


def print_result(result: Dict[str, Any]):
    """打印结果"""
    print(f"📊 执行结果:")
    print(f"   状态: {'✅ 成功' if result['success'] else '❌ 失败'}")
    print(f"   场景: {result.get('scenario', 'unknown')}")
    print(f"   重试次数: {result.get('retry_count', 0)}")
    
    if result.get('execution_time'):
        print(f"   执行时间: {result['execution_time']:.2f}秒")
    
    if result.get('summary'):
        print(f"   总结: {result['summary']}")
    
    # 显示生成的代码
    if result.get('generated_code'):
        print(f"\n📄 生成的代码:")
        print("```javascript")
        print(result['generated_code'])
        print("```")
    
    # 显示建议
    if result.get('suggestions'):
        print(f"\n💡 建议:")
        for suggestion in result['suggestions']:
            print(f"   • {suggestion}")
    
    # 显示错误信息
    if not result['success'] and result.get('error_diagnosis'):
        error_info = result['error_diagnosis']
        print(f"\n🔍 错误诊断:")
        print(f"   类型: {error_info.get('error_type', '未知')}")
        print(f"   消息: {error_info.get('message', '无详细信息')}")


def save_result_to_file(filename: str, result: Dict[str, Any]):
    """保存结果到文件"""
    try:
        # 创建输出目录
        output_dir = Path("output")
        output_dir.mkdir(exist_ok=True)
        
        # 保存JSON结果
        json_file = output_dir / f"{filename}_result.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 保存生成的代码
        if result.get('generated_code'):
            code_file = output_dir / f"{filename}_test.spec.js"
            with open(code_file, 'w', encoding='utf-8') as f:
                f.write(result['generated_code'])
            print(f"💾 代码已保存到: {code_file}")
        
        print(f"💾 结果已保存到: {json_file}")
        
    except Exception as e:
        print(f"⚠️ 保存文件失败: {str(e)}")


async def run_interactive_mode():
    """交互模式"""
    print("\n🎮 进入交互模式")
    print("请输入测试要求，输入 'quit' 退出")
    
    while True:
        try:
            print("\n" + "-" * 30)
            user_requirements = input("📝 请描述您的测试需求: ").strip()
            
            if user_requirements.lower() in ['quit', 'exit', 'q']:
                print("👋 再见！")
                break
            
            if not user_requirements:
                print("⚠️ 请输入有效的测试需求")
                continue
            
            # 获取DOM信息（可选）
            dom_info = input("🌐 请提供DOM信息（可选，直接回车跳过）: ").strip()
            
            # 获取现有脚本（可选）
            existing_script = input("📄 请提供现有脚本（可选，直接回车跳过）: ").strip()
            
            # 创建请求
            request = TestRequest(
                dom_info=dom_info if dom_info else None,
                user_requirements=user_requirements,
                existing_script=existing_script if existing_script else None,
                error_info=None
            )
            
            # 运行智能体
            print("\n🚀 正在处理您的请求...")
            result = await run_playwright_agent(request)
            
            # 显示结果
            print_result(result)
            
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")


def show_help():
    """显示帮助信息"""
    print("""
🎯 Playwright自动化测试智能体使用说明

📋 功能特性:
  • 智能分析测试需求和DOM结构
  • 自动生成高质量的Playwright测试代码
  • 智能错误诊断和修复建议
  • 支持代码修改和优化

🎮 使用方式:
  1. 运行示例: python main.py
  2. 交互模式: python main.py --interactive
  3. 显示帮助: python main.py --help

📝 输入格式:
  • DOM信息: 页面的HTML结构
  • 测试需求: 具体的测试步骤描述
  • 现有脚本: 需要修改的代码（可选）
  • 错误信息: 需要调试的错误（可选）

💡 提示:
  • 提供清晰的DOM结构有助于生成更准确的代码
  • 详细描述测试步骤可以获得更好的结果
  • 支持多种测试场景和复杂交互
    """)


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "--interactive":
            asyncio.run(run_interactive_mode())
        elif sys.argv[1] == "--help":
            show_help()
        else:
            print("❌ 未知参数，使用 --help 查看帮助")
    else:
        # 默认运行示例
        asyncio.run(main())
