"""智能体测试用例"""

import asyncio
import pytest
from pathlib import Path

from src.models.schemas import TestRequest
from src.agent.graph import run_playwright_agent


class TestPlaywrightAgent:
    """Playwright智能体测试类"""
    
    @pytest.mark.asyncio
    async def test_simple_login_form(self):
        """测试简单登录表单"""
        dom_info = """
        <form id="loginForm">
            <input type="text" id="username" placeholder="用户名">
            <input type="password" id="password" placeholder="密码">
            <button type="submit" id="loginBtn">登录</button>
        </form>
        """
        
        request = TestRequest(
            dom_info=dom_info,
            user_requirements="测试登录功能：输入用户名'admin'和密码'123456'，点击登录按钮",
            existing_script=None,
            error_info=None
        )
        
        result = await run_playwright_agent(request)
        
        # 验证结果
        assert result is not None
        assert "generated_code" in result
        assert result["generated_code"] is not None
        assert len(result["generated_code"]) > 0
        
        # 验证生成的代码包含关键元素
        code = result["generated_code"]
        assert "test(" in code
        assert "page.fill" in code or "page.type" in code
        assert "page.click" in code
        assert "#username" in code or "#password" in code
    
    @pytest.mark.asyncio
    async def test_shopping_cart_interaction(self):
        """测试购物车交互"""
        dom_info = """
        <div class="product-item" data-id="1">
            <h3>商品A</h3>
            <span class="price">¥99.00</span>
            <button class="add-to-cart" data-product-id="1">加入购物车</button>
        </div>
        <div class="cart">
            <span class="cart-count">0</span>
        </div>
        """
        
        request = TestRequest(
            dom_info=dom_info,
            user_requirements="点击加入购物车按钮，验证购物车数量变化",
            existing_script=None,
            error_info=None
        )
        
        result = await run_playwright_agent(request)
        
        # 验证结果
        assert result is not None
        assert result["generated_code"] is not None
        
        code = result["generated_code"]
        assert "add-to-cart" in code or "add_to_cart" in code
        assert "expect(" in code  # 应该有断言
    
    @pytest.mark.asyncio
    async def test_code_modification(self):
        """测试代码修改功能"""
        existing_script = """
        const { test, expect } = require('@playwright/test');
        
        test('点击测试', async ({ page }) => {
            await page.goto('https://example.com');
            await page.click('#oldButton');  // 需要修改的选择器
            await expect(page.locator('#result')).toBeVisible();
        });
        """
        
        request = TestRequest(
            dom_info='<button id="newButton">新按钮</button>',
            user_requirements="将点击目标改为id为newButton的按钮",
            existing_script=existing_script,
            error_info=None
        )
        
        result = await run_playwright_agent(request)
        
        # 验证结果
        assert result is not None
        assert result["generated_code"] is not None
        assert "#newButton" in result["generated_code"]
        assert "#oldButton" not in result["generated_code"]
    
    @pytest.mark.asyncio
    async def test_error_debugging(self):
        """测试错误调试功能"""
        existing_script = """
        const { test, expect } = require('@playwright/test');
        
        test('错误测试', async ({ page }) => {
            await page.goto('https://example.com');
            await page.click('#nonExistentButton');
        });
        """
        
        request = TestRequest(
            dom_info='<button id="realButton">真实按钮</button>',
            user_requirements="修复元素定位错误",
            existing_script=existing_script,
            error_info="Error: Timeout 30000ms exceeded. waiting for selector '#nonExistentButton' to be visible"
        )
        
        result = await run_playwright_agent(request)
        
        # 验证结果
        assert result is not None
        assert result["generated_code"] is not None
        assert "#realButton" in result["generated_code"]
        assert "#nonExistentButton" not in result["generated_code"]


def test_agent_capabilities():
    """测试智能体能力获取"""
    from src.agent.graph import get_agent_info
    
    info = get_agent_info()
    
    assert "name" in info
    assert "capabilities" in info
    assert "workflow" in info
    
    capabilities = info["capabilities"]
    assert "支持的测试场景" in capabilities
    assert "DOM分析能力" in capabilities
    assert "代码生成特性" in capabilities


if __name__ == "__main__":
    # 运行简单测试
    async def run_simple_test():
        print("🧪 运行简单测试...")
        
        # 测试登录表单
        dom_info = """
        <form id="loginForm">
            <input type="text" id="username" data-testid="username">
            <input type="password" id="password" data-testid="password">
            <button type="submit" id="loginBtn" data-testid="login-btn">登录</button>
        </form>
        <div id="message" data-testid="message"></div>
        """
        
        request = TestRequest(
            dom_info=dom_info,
            user_requirements="测试用户登录：输入用户名'testuser'和密码'password123'，点击登录按钮，验证登录结果",
            existing_script=None,
            error_info=None
        )
        
        try:
            result = await run_playwright_agent(request)
            
            print("✅ 测试完成！")
            print(f"生成的代码长度: {len(result.get('generated_code', ''))}")
            print(f"场景类型: {result.get('scenario')}")
            print(f"建议数量: {len(result.get('suggestions', []))}")
            
            if result.get('generated_code'):
                print("\n生成的代码预览:")
                print(result['generated_code'][:200] + "...")
            
        except Exception as e:
            print(f"❌ 测试失败: {str(e)}")
    
    asyncio.run(run_simple_test())
