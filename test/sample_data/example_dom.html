<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面示例</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, select, textarea { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .product-item { border: 1px solid #eee; padding: 15px; margin: 10px 0; border-radius: 4px; }
        .cart { position: fixed; top: 20px; right: 20px; background: #f8f9fa; padding: 10px; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>自动化测试示例页面</h1>
        
        <!-- 登录表单 -->
        <section id="login-section">
            <h2>用户登录</h2>
            <form id="loginForm" action="/login" method="POST">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" placeholder="请输入用户名" data-testid="username-input">
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" placeholder="请输入密码" data-testid="password-input">
                </div>
                <div class="form-group">
                    <button type="submit" id="loginBtn" data-testid="login-button">登录</button>
                </div>
            </form>
            <div id="loginMessage" style="display:none;" data-testid="login-message"></div>
        </section>

        <!-- 注册表单 -->
        <section id="register-section" style="margin-top: 40px;">
            <h2>用户注册</h2>
            <form id="registerForm" action="/register" method="POST">
                <div class="form-group">
                    <label for="regUsername">用户名:</label>
                    <input type="text" id="regUsername" name="username" data-testid="reg-username">
                </div>
                <div class="form-group">
                    <label for="email">邮箱:</label>
                    <input type="email" id="email" name="email" data-testid="reg-email">
                </div>
                <div class="form-group">
                    <label for="regPassword">密码:</label>
                    <input type="password" id="regPassword" name="password" data-testid="reg-password">
                </div>
                <div class="form-group">
                    <label for="confirmPassword">确认密码:</label>
                    <input type="password" id="confirmPassword" name="confirmPassword" data-testid="reg-confirm-password">
                </div>
                <div class="form-group">
                    <label for="gender">性别:</label>
                    <select id="gender" name="gender" data-testid="reg-gender">
                        <option value="">请选择</option>
                        <option value="male">男</option>
                        <option value="female">女</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="agreeTerms" name="agreeTerms" data-testid="agree-terms">
                        我同意服务条款
                    </label>
                </div>
                <div class="form-group">
                    <button type="submit" id="registerBtn" data-testid="register-button">注册</button>
                </div>
            </form>
        </section>

        <!-- 商品列表 -->
        <section id="products-section" style="margin-top: 40px;">
            <h2>商品列表</h2>
            <div class="product-list">
                <div class="product-item" data-id="1" data-testid="product-1">
                    <h3>MacBook Pro</h3>
                    <p class="description">苹果笔记本电脑</p>
                    <span class="price" data-testid="price-1">¥12999.00</span>
                    <button class="add-to-cart" data-product-id="1" data-testid="add-cart-1">加入购物车</button>
                </div>
                <div class="product-item" data-id="2" data-testid="product-2">
                    <h3>iPhone 15</h3>
                    <p class="description">苹果智能手机</p>
                    <span class="price" data-testid="price-2">¥5999.00</span>
                    <button class="add-to-cart" data-product-id="2" data-testid="add-cart-2">加入购物车</button>
                </div>
                <div class="product-item" data-id="3" data-testid="product-3">
                    <h3>iPad Air</h3>
                    <p class="description">苹果平板电脑</p>
                    <span class="price" data-testid="price-3">¥4599.00</span>
                    <button class="add-to-cart" data-product-id="3" data-testid="add-cart-3">加入购物车</button>
                </div>
            </div>
        </section>

        <!-- 搜索功能 -->
        <section id="search-section" style="margin-top: 40px;">
            <h2>商品搜索</h2>
            <div class="search-form">
                <input type="text" id="searchInput" placeholder="请输入商品名称" data-testid="search-input">
                <button id="searchBtn" data-testid="search-button">搜索</button>
            </div>
            <div id="searchResults" data-testid="search-results" style="margin-top: 20px;"></div>
        </section>

        <!-- 导航菜单 -->
        <nav id="main-nav" style="margin-top: 40px;">
            <h2>网站导航</h2>
            <ul>
                <li><a href="/" data-testid="nav-home">首页</a></li>
                <li><a href="/products" data-testid="nav-products">商品</a></li>
                <li><a href="/cart" data-testid="nav-cart">购物车</a></li>
                <li><a href="/orders" data-testid="nav-orders">订单</a></li>
                <li><a href="/profile" data-testid="nav-profile">个人中心</a></li>
                <li><a href="/help" data-testid="nav-help">帮助</a></li>
            </ul>
        </nav>
    </div>

    <!-- 购物车悬浮窗 -->
    <div class="cart" data-testid="cart-widget">
        <span>购物车 (<span class="cart-count" data-testid="cart-count">0</span>)</span>
        <a href="/cart" class="cart-link" data-testid="cart-link">查看购物车</a>
    </div>

    <!-- 消息提示 -->
    <div id="notification" data-testid="notification" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#333; color:white; padding:10px; border-radius:4px;"></div>

    <script>
        // 简单的交互逻辑示例
        document.addEventListener('DOMContentLoaded', function() {
            // 登录表单提交
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                setTimeout(() => {
                    const message = document.getElementById('loginMessage');
                    if (username === 'admin' && password === '123456') {
                        message.textContent = '登录成功！';
                        message.style.color = 'green';
                    } else {
                        message.textContent = '用户名或密码错误！';
                        message.style.color = 'red';
                    }
                    message.style.display = 'block';
                }, 1000);
            });

            // 购物车功能
            let cartCount = 0;
            document.querySelectorAll('.add-to-cart').forEach(btn => {
                btn.addEventListener('click', function() {
                    cartCount++;
                    document.querySelector('.cart-count').textContent = cartCount;
                    showNotification('商品已加入购物车！');
                });
            });

            // 搜索功能
            document.getElementById('searchBtn').addEventListener('click', function() {
                const query = document.getElementById('searchInput').value;
                const results = document.getElementById('searchResults');
                
                setTimeout(() => {
                    if (query) {
                        results.innerHTML = `<p>搜索结果：找到 ${Math.floor(Math.random() * 10) + 1} 个相关商品</p>`;
                    } else {
                        results.innerHTML = '<p>请输入搜索关键词</p>';
                    }
                }, 500);
            });

            function showNotification(message) {
                const notification = document.getElementById('notification');
                notification.textContent = message;
                notification.style.display = 'block';
                setTimeout(() => {
                    notification.style.display = 'none';
                }, 2000);
            }
        });
    </script>
</body>
</html>
