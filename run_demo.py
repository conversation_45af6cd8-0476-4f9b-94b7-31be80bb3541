#!/usr/bin/env python3
"""简化的演示启动脚本"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_dependencies():
    """检查关键依赖"""
    missing_deps = []
    
    try:
        import pydantic
        print("✅ pydantic 可用")
    except ImportError:
        missing_deps.append("pydantic")
    
    try:
        from src.models.schemas import TestRequest
        print("✅ 本地模块可用")
    except ImportError as e:
        print(f"❌ 本地模块导入失败: {e}")
        return False
    
    if missing_deps:
        print(f"❌ 缺少依赖: {', '.join(missing_deps)}")
        print("请运行: pip3 install -r requirements.txt")
        return False
    
    return True

async def simple_demo():
    """简化的演示"""
    print("🎯 Playwright智能体简化演示")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    try:
        from src.models.schemas import TestRequest
        
        # 创建一个简单的测试请求
        request = TestRequest(
            dom_info="""
            <form id="loginForm">
                <input type="text" id="username" placeholder="用户名">
                <input type="password" id="password" placeholder="密码">
                <button type="submit" id="loginBtn">登录</button>
            </form>
            """,
            user_requirements="测试登录功能：输入用户名和密码，点击登录按钮"
        )
        
        print("✅ 成功创建测试请求")
        print(f"📝 DOM信息长度: {len(request.dom_info)} 字符")
        print(f"📋 测试需求: {request.user_requirements}")
        
        # 显示项目结构
        print(f"\n📂 项目结构:")
        project_files = [
            "src/models/schemas.py",
            "src/agent/nodes.py", 
            "src/agent/graph.py",
            "src/tools/dom_analyzer.py",
            "src/tools/playwright_runner.py",
            "main.py",
            "requirements.txt"
        ]
        
        for file_path in project_files:
            if Path(file_path).exists():
                print(f"  ✅ {file_path}")
            else:
                print(f"  ❌ {file_path}")
        
        print(f"\n🎉 智能体项目结构完整！")
        print(f"\n📋 下一步操作:")
        print(f"  1. 安装完整依赖: pip3 install -r requirements.txt")
        print(f"  2. 运行完整演示: python3 main.py")
        print(f"  3. 交互模式: python3 main.py --interactive")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        print(f"📝 错误详情: {type(e).__name__}")
        return False

def show_project_info():
    """显示项目信息"""
    print("🤖 Playwright自动化测试智能体")
    print("=" * 50)
    
    features = [
        "🎯 智能分析DOM结构和测试需求",
        "🔧 自动生成高质量Playwright测试代码", 
        "🔍 智能错误诊断和修复建议",
        "⚡ 代码优化和性能建议",
        "🔄 自动重试和异常处理",
        "📊 详细的测试报告和分析"
    ]
    
    print("🚀 核心功能:")
    for feature in features:
        print(f"  {feature}")
    
    scenarios = [
        "登录/注册表单测试",
        "电商购物车功能测试", 
        "搜索和筛选功能测试",
        "页面导航和跳转测试",
        "表单验证和提交测试",
        "动态内容加载测试"
    ]
    
    print(f"\n📋 支持场景:")
    for scenario in scenarios:
        print(f"  • {scenario}")

def main():
    """主函数"""
    try:
        show_project_info()
        print("\n" + "="*50)
        asyncio.run(simple_demo())
    except KeyboardInterrupt:
        print("\n👋 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 运行失败: {str(e)}")

if __name__ == "__main__":
    main()
